<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>库存分配管理</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/static/layui/css/layui.css">
    <style>
        .layui-table-cell {
            height: auto !important;
            white-space: normal;
        }
        .allocation-container {
            padding: 15px;
        }
    </style>
</head>
<body>
    <div class="allocation-container">
        <div class="layui-card">
            <div class="layui-card-header">
                <h2>库存分配需求管理</h2>
            </div>
            <div class="layui-card-body">
                    
                    <!-- 搜索栏 -->
                    <form class="layui-form" lay-filter="searchForm">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">业务类型</label>
                                <div class="layui-input-inline">
                                    <select name="ref_type">
                                        <option value="">全部</option>
                                        <option value="customer_order">客户订单</option>
                                        <option value="production_order">生产订单</option>
                                        <option value="purchase_order">采购订单</option>
                                        <option value="transfer">调拨单</option>
                                        <option value="quality_check">质检</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">状态</label>
                                <div class="layui-input-inline">
                                    <select name="status">
                                        <option value="">全部</option>
                                        <option value="1">待分配</option>
                                        <option value="2">部分分配</option>
                                        <option value="3">完全分配</option>
                                        <option value="4">已取消</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">业务单号</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="ref_no" placeholder="请输入业务单号" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" lay-submit lay-filter="search">搜索</button>
                                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                            </div>
                        </div>
                    </form>
                    
                    <!-- 操作按钮 -->
                    <div class="layui-btn-group" style="margin-bottom: 15px;">
                        <button class="layui-btn layui-btn-normal" onclick="getStatistics()">获取统计</button>
                        <button class="layui-btn layui-btn-warm" onclick="cleanupExpired()">清理过期</button>
                        <button class="layui-btn layui-btn-primary" onclick="refreshTable()">刷新</button>
                    </div>
                    
                    <!-- 数据表格 -->
                    <table class="layui-hide" id="allocationTable" lay-filter="allocationTable"></table>
                    
                </div>
            </div>
        </div>
    </div>

    <!-- 手动分配弹窗 -->
    <div id="manualAllocateModal" style="display: none; padding: 20px;">
        <form class="layui-form" lay-filter="manualAllocateForm">
            <input type="hidden" name="request_id" id="modal_request_id">
            <div class="layui-form-item">
                <label class="layui-form-label">产品信息</label>
                <div class="layui-input-block">
                    <span id="modal_product_info"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">需求数量</label>
                <div class="layui-input-block">
                    <span id="modal_need_quantity"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">分配数量</label>
                <div class="layui-input-block">
                    <input type="number" name="allocate_quantity" placeholder="请输入分配数量" class="layui-input" required>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="submitManualAllocate">确认分配</button>
                    <button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
                </div>
            </div>
        </form>
    </div>

    <!-- 操作列模板 -->
    <script type="text/html" id="operateBar">
        {{# if(d.status == 1 || d.status == 2) { }}
            <button class="layui-btn layui-btn-xs layui-btn-normal" onclick="manualAllocate({{d.id}})">手动分配</button>
        {{# } }}
        {{# if(d.status != 4) { }}
            <button class="layui-btn layui-btn-xs layui-btn-danger" onclick="cancelRequest({{d.id}})">取消</button>
        {{# } }}
    </script>

    <!-- 状态模板 -->
    <script type="text/html" id="statusBar">
        {{# if(d.status == 1) { }}
            <span class="layui-badge layui-bg-orange">待分配</span>
        {{# } else if(d.status == 2) { }}
            <span class="layui-badge layui-bg-blue">部分分配</span>
        {{# } else if(d.status == 3) { }}
            <span class="layui-badge layui-bg-green">完全分配</span>
        {{# } else if(d.status == 4) { }}
            <span class="layui-badge layui-bg-gray">已取消</span>
        {{# } }}
    </script>

    <!-- 优先级模板 -->
    <script type="text/html" id="priorityBar">
        {{# if(d.priority >= 90) { }}
            <span style="color: #FF5722; font-weight: bold;">{{d.priority}}</span>
        {{# } else if(d.priority >= 70) { }}
            <span style="color: #FF9800; font-weight: bold;">{{d.priority}}</span>
        {{# } else if(d.priority >= 50) { }}
            <span style="color: #2196F3;">{{d.priority}}</span>
        {{# } else { }}
            <span style="color: #9E9E9E;">{{d.priority}}</span>
        {{# } }}
    </script>

    <script src="/static/layui/layui.js"></script>
    <script src="/static/js/jquery.min.js"></script>
    <script>
        layui.use(['table', 'form', 'layer', 'util'], function(){
            var table = layui.table;
            var form = layui.form;
            var layer = layui.layer;
            var util = layui.util;
            var $ = layui.jquery;
            
            // 渲染表格
            table.render({
                elem: '#allocationTable',
                url: '/warehouse/allocation_manage/index',
                page: true,
                limit: 15,
                limits: [15, 30, 50, 100],
                cols: [[
                    {field: 'id', title: 'ID', width: 80, sort: true},
                    {field: 'ref_type_text', title: '业务类型', width: 120},
                    {field: 'ref_no', title: '业务单号', width: 150},
                    {field: 'product_name', title: '产品名称', width: 200, templet: function(d){
                        return (d.product && d.product.title) || '未知产品';
                    }},
                    {field: 'warehouse_name', title: '仓库', width: 120, templet: function(d){
                        return (d.warehouse && d.warehouse.name) || '未知仓库';
                    }},
                    {field: 'quantity', title: '需求数量', width: 100, sort: true},
                    {field: 'allocated_quantity', title: '已分配', width: 100, sort: true},
                    {field: 'pending_quantity', title: '待分配', width: 100, templet: function(d){
                        return d.quantity - d.allocated_quantity;
                    }},
                    {field: 'priority', title: '优先级', width: 100, templet: '#priorityBar', sort: true},
                    {field: 'status', title: '状态', width: 120, templet: '#statusBar'},
                    {field: 'request_time', title: '请求时间', width: 160, templet: function(d){
                        return util.toDateString(d.request_time * 1000, 'yyyy-MM-dd HH:mm');
                    }},
                    {field: 'wait_days', title: '等待天数', width: 100, templet: function(d){
                        var days = Math.ceil((Date.now() / 1000 - d.request_time) / 86400);
                        var color = days > 7 ? '#FF5722' : (days > 3 ? '#FF9800' : '#666');
                        return '<span style="color: ' + color + '">' + days + '天</span>';
                    }},
                    {title: '操作', width: 200, templet: '#operateBar', fixed: 'right'}
                ]]
            });
            
            // 搜索
            form.on('submit(search)', function(data){
                table.reload('allocationTable', {
                    where: data.field,
                    page: {curr: 1}
                });
                return false;
            });
            
            // 手动分配表单提交
            form.on('submit(submitManualAllocate)', function(data){
                $.post('/warehouse/allocation_manage/manualAllocate', data.field, function(res){
                    if(res.code == 0){
                        layer.msg('分配成功', {icon: 1});
                        layer.closeAll();
                        table.reload('allocationTable');
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                });
                return false;
            });
        });
        
        // 手动分配
        function manualAllocate(requestId) {
            // 获取分配需求详情
            $.get('/warehouse/allocation_manage/index', {request_id: requestId}, function(res){
                if(res.code == 0 && res.data.length > 0) {
                    var data = res.data[0];
                    $('#modal_request_id').val(data.id);
                    $('#modal_product_info').text((data.product ? data.product.title : '未知产品') + ' (' + (data.product ? data.product.material_code : '') + ')');
                    $('#modal_need_quantity').text((data.quantity - data.allocated_quantity) + ' (总需求: ' + data.quantity + ', 已分配: ' + data.allocated_quantity + ')');
                    
                    layer.open({
                        type: 1,
                        title: '手动分配库存',
                        content: $('#manualAllocateModal'),
                        area: ['500px', '400px'],
                        btn: false
                    });
                } else {
                    layer.msg('获取分配需求详情失败', {icon: 2});
                }
            });
        }
        
        // 取消分配需求
        function cancelRequest(requestId) {
            layer.prompt({title: '请输入取消原因', formType: 2}, function(reason, index){
                $.post('/warehouse/allocation_manage/cancelRequest', {
                    request_id: requestId,
                    reason: reason
                }, function(res){
                    if(res.code == 0){
                        layer.msg('取消成功', {icon: 1});
                        layer.close(index);
                        layui.table.reload('allocationTable');
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                });
            });
        }
        
        // 获取统计
        function getStatistics() {
            $.get('/warehouse/allocation_manage/statistics', function(res){
                if(res.code == 0) {
                    var content = '<div style="padding: 20px;">';
                    content += '<h3>分配统计</h3>';
                    content += '<p>今日分配: ' + (res.data.today_stats.count || 0) + ' 次，总数量: ' + (res.data.today_stats.total_allocated || 0) + '</p>';
                    content += '<h4>按状态统计:</h4>';
                    if(res.data.status_stats && res.data.status_stats.length > 0) {
                        res.data.status_stats.forEach(function(item) {
                            var statusText = ['', '待分配', '部分分配', '完全分配', '已取消'][item.status] || '未知';
                            content += '<p>' + statusText + ': ' + item.count + ' 条，总数量: ' + item.total_quantity + '</p>';
                        });
                    }
                    content += '</div>';
                    
                    layer.open({
                        type: 1,
                        title: '分配统计',
                        content: content,
                        area: ['500px', '400px']
                    });
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            });
        }
        
        // 清理过期需求
        function cleanupExpired() {
            layer.confirm('确定要清理过期的分配需求吗？', function(index){
                $.post('/warehouse/allocation_manage/cleanupExpired', {max_wait_days: 30}, function(res){
                    if(res.code == 0) {
                        layer.msg(res.msg, {icon: 1});
                        layui.table.reload('allocationTable');
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                });
                layer.close(index);
            });
        }
        
        // 刷新表格
        function refreshTable() {
            layui.table.reload('allocationTable');
        }
    </script>
</body>
</html>
