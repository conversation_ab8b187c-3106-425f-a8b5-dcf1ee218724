<?php
declare (strict_types = 1);

namespace app\warehouse\controller;

use app\base\BaseController;
use think\facade\Db;
use think\facade\View;
use app\warehouse\service\InventoryAllocationService;
use app\warehouse\model\InventoryAllocationRequest;
use app\warehouse\model\InventoryAllocationHistory;
use app\warehouse\model\ReverseAuditLog;

/**
 * 库存分配管理控制器
 */
class AllocationManage extends BaseController
{
    /**
     * 分配需求列表
     */
    public function index()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $where = [];
            
            // 搜索条件
            if (!empty($param['product_id'])) {
                $where[] = ['product_id', '=', $param['product_id']];
            }
            
            if (!empty($param['warehouse_id'])) {
                $where[] = ['warehouse_id', '=', $param['warehouse_id']];
            }
            
            if (!empty($param['ref_type'])) {
                $where[] = ['ref_type', '=', $param['ref_type']];
            }
            
            if (!empty($param['status'])) {
                $where[] = ['status', '=', $param['status']];
            }
            
            if (!empty($param['ref_no'])) {
                $where[] = ['ref_no', 'like', '%' . $param['ref_no'] . '%'];
            }
            
            $list = InventoryAllocationRequest::with(['product', 'warehouse', 'creator'])
                ->where($where)
                ->order('priority DESC, request_time ASC')
                ->paginate([
                    'list_rows' => $param['limit'] ?? 15,
                    'page' => $param['page'] ?? 1
                ]);

            // 处理数据，添加计算字段
            $items = $list->items();
            foreach ($items as &$item) {
                $item['pending_quantity'] = $item['quantity'] - $item['allocated_quantity'];
                $item['wait_days'] = ceil((time() - $item['request_time']) / 86400);

                // 状态文本
                $statusMap = [1 => '待分配', 2 => '部分分配', 3 => '完全分配', 4 => '已取消'];
                $item['status_text'] = $statusMap[$item['status']] ?? '未知';

                // 业务类型文本
                $typeMap = [
                    'customer_order' => '客户订单',
                    'production_order' => '生产订单',
                    'purchase_order' => '采购订单',
                    'transfer' => '调拨单',
                    'quality_check' => '质检'
                ];
                $item['ref_type_text'] = $typeMap[$item['ref_type']] ?? '未知类型';
            }

            $data = [
                'code' => 0,
                'msg' => '',
                'count' => $list->total(),
                'data' => $items
            ];
            
            return json($data);
        }
        
        // 获取产品列表
        $products = Db::name('product')
            ->field('id, title, material_code, unit, specs')
            ->where('status', 1)
            ->limit(100)
            ->select()
            ->toArray();

        // 获取仓库列表
        $warehouses = Db::name('warehouse')
            ->field('id, title')
            ->where('status', 1)
            ->select()
            ->toArray();
        
        View::assign([
            'products' => $products,
            'warehouses' => $warehouses
        ]);
        
        return View::fetch();
    }
    
    /**
     * 分配历史记录
     */
    public function history()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $where = [];
            
            // 搜索条件
            if (!empty($param['product_id'])) {
                $where[] = ['product_id', '=', $param['product_id']];
            }
            
            if (!empty($param['allocation_type'])) {
                $where[] = ['allocation_type', '=', $param['allocation_type']];
            }
            
            $list = InventoryAllocationHistory::with(['product', 'warehouse', 'creator', 'allocationRequest'])
                ->where($where)
                ->order('create_time DESC')
                ->paginate([
                    'list_rows' => $param['limit'] ?? 15,
                    'page' => $param['page'] ?? 1
                ]);
            
            $data = [
                'code' => 0,
                'msg' => '',
                'count' => $list->total(),
                'data' => $list->items()
            ];
            
            return json($data);
        }
        
        return View::fetch();
    }
    
    /**
     * 反审日志
     */
    public function reverseLog()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $where = [];
            
            // 搜索条件
            if (!empty($param['ref_type'])) {
                $where[] = ['ref_type', '=', $param['ref_type']];
            }
            
            if (!empty($param['ref_no'])) {
                $where[] = ['ref_no', 'like', '%' . $param['ref_no'] . '%'];
            }
            
            $list = ReverseAuditLog::with(['operator'])
                ->where($where)
                ->order('create_time DESC')
                ->paginate([
                    'list_rows' => $param['limit'] ?? 15,
                    'page' => $param['page'] ?? 1
                ]);
            
            $data = [
                'code' => 0,
                'msg' => '',
                'count' => $list->total(),
                'data' => $list->items()
            ];
            
            return json($data);
        }
        
        return View::fetch();
    }
    
    /**
     * 手动分配
     */
    public function manualAllocate()
    {
        if (request()->isPost()) {
            $param = get_params();
            
            $requestId = $param['request_id'] ?? 0;
            $allocateQty = $param['allocate_quantity'] ?? 0;
            
            if (!$requestId || !$allocateQty) {
                return json(['code' => 1, 'msg' => '参数错误']);
            }
            
            try {
                $allocationRequest = InventoryAllocationRequest::find($requestId);
                if (!$allocationRequest) {
                    return json(['code' => 1, 'msg' => '分配需求不存在']);
                }
                
                $allocationService = new InventoryAllocationService();
                
                // 构造分配请求
                $request = [
                    'product_id' => $allocationRequest->product_id,
                    'warehouse_id' => $allocationRequest->warehouse_id,
                    'quantity' => $allocateQty,
                    'ref_type' => $allocationRequest->ref_type,
                    'ref_id' => $allocationRequest->ref_id,
                    'ref_no' => $allocationRequest->ref_no,
                    'notes' => '手动分配',
                    'created_by' => session('admin.id')
                ];
                
                $result = $allocationService->allocateAndLock($request);
                
                if ($result['code'] == 0) {
                    return json(['code' => 0, 'msg' => '手动分配成功', 'data' => $result]);
                } else {
                    return json(['code' => 1, 'msg' => '分配失败：' . $result['msg']]);
                }
                
            } catch (\Exception $e) {
                return json(['code' => 1, 'msg' => '分配失败：' . $e->getMessage()]);
            }
        }
        
        return json(['code' => 1, 'msg' => '非法请求']);
    }
    
    /**
     * 取消分配需求
     */
    public function cancelRequest()
    {
        if (request()->isPost()) {
            $param = get_params();
            
            $requestId = $param['request_id'] ?? 0;
            $reason = $param['reason'] ?? '';
            
            if (!$requestId) {
                return json(['code' => 1, 'msg' => '参数错误']);
            }
            
            try {
                $allocationRequest = InventoryAllocationRequest::find($requestId);
                if (!$allocationRequest) {
                    return json(['code' => 1, 'msg' => '分配需求不存在']);
                }
                
                if ($allocationRequest->status == 4) {
                    return json(['code' => 1, 'msg' => '分配需求已取消']);
                }
                
                $allocationRequest->cancel($reason);
                
                return json(['code' => 0, 'msg' => '取消成功']);
                
            } catch (\Exception $e) {
                return json(['code' => 1, 'msg' => '取消失败：' . $e->getMessage()]);
            }
        }
        
        return json(['code' => 1, 'msg' => '非法请求']);
    }
    
    /**
     * 分配统计
     */
    public function statistics()
    {
        try {
            // 按状态统计分配需求
            $statusStats = InventoryAllocationRequest::field([
                'status',
                'COUNT(*) as count',
                'SUM(quantity) as total_quantity',
                'SUM(allocated_quantity) as allocated_quantity'
            ])
            ->group('status')
            ->select()
            ->toArray();
            
            // 按业务类型统计
            $typeStats = InventoryAllocationRequest::field([
                'ref_type',
                'COUNT(*) as count',
                'SUM(quantity - allocated_quantity) as pending_quantity'
            ])
            ->where('status', 'in', [1, 2]) // 待分配和部分分配
            ->group('ref_type')
            ->select()
            ->toArray();
            
            // 今日分配统计
            $todayStart = strtotime(date('Y-m-d'));
            $todayStats = InventoryAllocationHistory::where('create_time', '>=', $todayStart)
                ->field([
                    'COUNT(*) as count',
                    'SUM(allocated_quantity) as total_allocated'
                ])
                ->find();
            
            return json([
                'code' => 0,
                'msg' => '获取成功',
                'data' => [
                    'status_stats' => $statusStats,
                    'type_stats' => $typeStats,
                    'today_stats' => $todayStats
                ]
            ]);
            
        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '获取统计失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 批量处理过期需求
     */
    public function cleanupExpired()
    {
        if (request()->isPost()) {
            try {
                $maxWaitDays = input('max_wait_days', 30);
                $count = InventoryAllocationRequest::cleanupExpired($maxWaitDays);
                
                return json([
                    'code' => 0,
                    'msg' => "清理完成，共处理 {$count} 条过期需求"
                ]);
                
            } catch (\Exception $e) {
                return json(['code' => 1, 'msg' => '清理失败：' . $e->getMessage()]);
            }
        }
        
        return json(['code' => 1, 'msg' => '非法请求']);
    }
}
