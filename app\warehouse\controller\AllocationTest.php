<?php
declare (strict_types = 1);

namespace app\warehouse\controller;

use app\base\BaseController;
use think\facade\Db;
use think\facade\View;
use app\warehouse\service\InventoryAllocationService;
use app\warehouse\service\AllocationPriority;
use app\warehouse\model\InventoryAllocationRequest;

/**
 * 库存分配功能测试控制器
 */
class AllocationTest extends BaseController
{
    /**
     * 测试首页
     */
    public function index()
    {
        return View::fetch();
    }
    
    /**
     * 测试基础分配功能
     */
    public function testBasicAllocation()
    {
        $allocationService = new InventoryAllocationService();
        $results = [];
        
        try {
            // 测试数据
            $testProductId = 1; // 假设产品ID为1
            $testWarehouseId = 1; // 假设仓库ID为1
            
            // 测试1：检查当前库存
            $currentStock = Db::name('inventory_realtime')
                ->where('product_id', $testProductId)
                ->where('warehouse_id', $testWarehouseId)
                ->find();
                
            $results[] = [
                'test' => '当前库存检查',
                'status' => 'success',
                'data' => $currentStock ?: '无库存记录'
            ];
            
            // 测试2：创建分配需求
            $request = [
                'product_id' => $testProductId,
                'warehouse_id' => $testWarehouseId,
                'quantity' => 10,
                'ref_type' => 'customer_order',
                'ref_id' => 99999,
                'ref_no' => 'TEST_ORDER_001',
                'notes' => '测试分配需求',
                'created_by' => session('admin.id') ?: 1
            ];
            
            $allocationResult = $allocationService->allocateAndLock($request);
            $results[] = [
                'test' => '分配测试',
                'status' => $allocationResult['code'] == 0 ? 'success' : 'error',
                'data' => $allocationResult
            ];
            
            // 测试3：查询分配需求
            $allocationRequests = InventoryAllocationRequest::where('product_id', $testProductId)
                ->where('ref_type', 'customer_order')
                ->limit(5)
                ->select();
                
            $results[] = [
                'test' => '分配需求查询',
                'status' => 'success',
                'data' => $allocationRequests->toArray()
            ];
            
            // 测试4：优先级计算
            $priority1 = AllocationPriority::getDefaultPriority('customer_order');
            $priority2 = AllocationPriority::getDefaultPriority('production_order');
            $priority3 = AllocationPriority::calculatePriority('customer_order', 99999, [
                'urgency' => 'urgent',
                'is_vip' => true
            ]);
            
            $results[] = [
                'test' => '优先级计算',
                'status' => 'success',
                'data' => [
                    'customer_order_default' => $priority1,
                    'production_order_default' => $priority2,
                    'customer_order_dynamic' => $priority3
                ]
            ];
            
        } catch (\Exception $e) {
            $results[] = [
                'test' => '异常捕获',
                'status' => 'error',
                'data' => $e->getMessage()
            ];
        }
        
        return json(['code' => 0, 'msg' => '测试完成', 'data' => $results]);
    }
    
    /**
     * 测试入库自动分配
     */
    public function testAutoAllocation()
    {
        $allocationService = new InventoryAllocationService();
        
        $productId = input('product_id', 1);
        $warehouseId = input('warehouse_id', 1);
        $quantity = input('quantity', 50);
        
        try {
            $result = $allocationService->autoAllocateOnInbound($productId, $warehouseId, $quantity);
            
            return json([
                'code' => 0,
                'msg' => '自动分配测试完成',
                'data' => $result
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 1,
                'msg' => '测试失败：' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 测试反审释放
     */
    public function testReverseAudit()
    {
        $allocationService = new InventoryAllocationService();
        
        $refType = input('ref_type', 'customer_order');
        $refId = input('ref_id', 99999);
        $reason = input('reason', '测试反审');
        
        try {
            $result = $allocationService->releaseOnReverseAudit([
                'ref_type' => $refType,
                'ref_id' => $refId,
                'operator_id' => session('admin.id') ?: 1,
                'reason' => $reason
            ]);
            
            return json([
                'code' => 0,
                'msg' => '反审测试完成',
                'data' => $result
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 1,
                'msg' => '测试失败：' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 获取分配统计
     */
    public function getAllocationStats()
    {
        $productId = input('product_id');
        $warehouseId = input('warehouse_id');
        $refType = input('ref_type');
        
        try {
            $stats = InventoryAllocationRequest::getAllocationStats($productId, $warehouseId, $refType);
            
            return json([
                'code' => 0,
                'msg' => '统计获取成功',
                'data' => $stats
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 1,
                'msg' => '统计获取失败：' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 清理测试数据
     */
    public function cleanupTestData()
    {
        try {
            // 清理测试相关的分配需求
            Db::name('inventory_allocation_request')
                ->where('ref_no', 'like', 'TEST_%')
                ->delete();
                
            // 清理测试相关的锁定记录
            Db::name('inventory_lock')
                ->where('ref_no', 'like', 'TEST_%')
                ->delete();
                
            // 清理测试相关的分配历史
            Db::name('inventory_allocation_history')
                ->where('notes', 'like', '%测试%')
                ->delete();
                
            return json([
                'code' => 0,
                'msg' => '测试数据清理完成'
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 1,
                'msg' => '清理失败：' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 创建测试库存数据
     */
    public function createTestInventory()
    {
        $productId = input('product_id', 9999);
        $warehouseId = input('warehouse_id', 1);
        $quantity = input('quantity', 100);
        
        try {
            // 检查是否已存在
            $exists = Db::name('inventory_realtime')
                ->where('product_id', $productId)
                ->where('warehouse_id', $warehouseId)
                ->find();
                
            if ($exists) {
                // 更新库存
                Db::name('inventory_realtime')
                    ->where('product_id', $productId)
                    ->where('warehouse_id', $warehouseId)
                    ->update([
                        'quantity' => $quantity,
                        'available_quantity' => $quantity,
                        'locked_quantity' => 0,
                        'update_time' => time()
                    ]);
            } else {
                // 创建新库存
                Db::name('inventory_realtime')->insert([
                    'product_id' => $productId,
                    'warehouse_id' => $warehouseId,
                    'quantity' => $quantity,
                    'available_quantity' => $quantity,
                    'locked_quantity' => 0,
                    'create_time' => time(),
                    'update_time' => time()
                ]);
            }
            
            return json([
                'code' => 0,
                'msg' => '测试库存创建成功',
                'data' => [
                    'product_id' => $productId,
                    'warehouse_id' => $warehouseId,
                    'quantity' => $quantity
                ]
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 1,
                'msg' => '创建失败：' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 批量测试
     */
    public function batchTest()
    {
        $results = [];
        
        // 1. 创建测试库存
        $results['create_inventory'] = $this->createTestInventory();
        
        // 2. 基础分配测试
        $results['basic_allocation'] = $this->testBasicAllocation();
        
        // 3. 自动分配测试
        $results['auto_allocation'] = $this->testAutoAllocation();
        
        // 4. 获取统计
        $results['stats'] = $this->getAllocationStats();
        
        return json([
            'code' => 0,
            'msg' => '批量测试完成',
            'data' => $results
        ]);
    }
}
