<?php
declare (strict_types = 1);

namespace app\warehouse\controller;
use app\base\BaseController;
use app\warehouse\model\InventoryLock as InventoryLockModel;
use app\warehouse\service\InventoryLockService;
use think\facade\View;
use think\facade\Db;

/**
 * 库存锁定管理控制器
 */
class InventoryLock extends BaseController
{
    /**
     * 库存锁定列表页面
     */
    public function index()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $where = [];
            
            // 搜索条件
            if (!empty($param['keywords'])) {
                $where[] = ['p.title|p.material_code|il.ref_no', 'like', '%' . $param['keywords'] . '%'];
            }
            if (!empty($param['warehouse_id'])) {
                $where[] = ['il.warehouse_id', '=', $param['warehouse_id']];
            }
            if (isset($param['status']) && $param['status'] !== '') {
                $where[] = ['il.status', '=', $param['status']];
            }
            if (!empty($param['ref_type'])) {
                $where[] = ['il.ref_type', '=', $param['ref_type']];
            }
            if (!empty($param['create_time'])) {
                $timeRange = explode(' - ', $param['create_time']);
                if (count($timeRange) == 2) {
                    $where[] = ['il.create_time', 'between', [strtotime($timeRange[0]), strtotime($timeRange[1] . ' 23:59:59')]];
                }
            }
            
            // 查询数据
            $list = Db::name('inventory_lock')
                ->alias('il')
                ->join('product p', 'il.product_id = p.id', 'left')
                ->join('warehouse w', 'il.warehouse_id = w.id', 'left')
                ->join('admin a', 'il.created_by = a.id', 'left')
                ->field('il.*, p.title as product_name, p.material_code, p.unit, 
                        w.name as warehouse_name, a.name as creator_name')
                ->where($where)
                ->order('il.id desc')
                ->paginate([
                    'list_rows' => input('limit', 15),
                    'page' => input('page', 1),
                ]);
            
            return json([
                'code' => 0,
                'msg' => '',
                'count' => $list->total(),
                'data' => $list->items()
            ]);
        } else {
            // 获取仓库列表
            $warehouses = Db::name('warehouse')
                ->field('id, name')
                ->where('status', 1)
                ->select();
            
            // 获取状态选项
            $statusArr = InventoryLockModel::getStatusArr();
            
            // 获取关联类型选项
            $refTypeArr = InventoryLockModel::getRefTypeArr();
            
            View::assign([
                'warehouses' => $warehouses,
                'statusArr' => $statusArr,
                'refTypeArr' => $refTypeArr
            ]);
            
            return View::fetch();
        }
    }
    
    /**
     * 锁定库存
     */
    public function lock()
    {
        if (request()->isPost()) {
            $param = get_params();
            
            try {
                $service = new InventoryLockService();
                $result = $service->lockInventory([
                    'product_id' => $param['product_id'],
                    'warehouse_id' => $param['warehouse_id'],
                    'quantity' => $param['quantity'],
                    'ref_type' => $param['ref_type'],
                    'ref_id' => $param['ref_id'],
                    'ref_no' => $param['ref_no'] ?? '',
                    'notes' => $param['notes'] ?? '',
                    'created_by' => $this->uid
                ]);
                
                return json(['code' => 0, 'msg' => '库存锁定成功', 'data' => $result]);
            } catch (\Exception $e) {
                return json(['code' => 1, 'msg' => '库存锁定失败：' . $e->getMessage()]);
            }
        }
        
        // 获取产品列表
        $products = Db::name('product')
            ->field('id, title, material_code, unit')
            ->where('status', 1)
            ->select();
        
        // 获取仓库列表
        $warehouses = Db::name('warehouse')
            ->field('id, name')
            ->where('status', 1)
            ->select();
        
        View::assign([
            'products' => $products,
            'warehouses' => $warehouses
        ]);
        
        return View::fetch();
    }
    
    /**
     * 释放锁定
     */
    public function unlock()
    {
        if (request()->isPost()) {
            $param = get_params();
            $lockId = $param['id'] ?? 0;

            if (empty($lockId)) {
                return json(['code' => 1, 'msg' => '参数错误']);
            }

            try {
                $service = new InventoryLockService();
                $result = $service->releaseLockedInventory([$lockId]);

                return json(['code' => 0, 'msg' => '释放锁定成功', 'data' => $result]);
            } catch (\Exception $e) {
                return json(['code' => 1, 'msg' => '释放锁定失败：' . $e->getMessage()]);
            }
        }

        return json(['code' => 1, 'msg' => '非法请求']);
    }
    
    /**
     * 使用锁定库存
     */
    public function useLock()
    {
        if (request()->isPost()) {
            $param = get_params();
            $lockId = $param['id'] ?? 0;

            if (empty($lockId)) {
                return json(['code' => 1, 'msg' => '参数错误']);
            }

            try {
                $service = new InventoryLockService();
                $result = $service->useLockedInventory([$lockId]);

                return json(['code' => 0, 'msg' => '使用锁定库存成功', 'data' => $result]);
            } catch (\Exception $e) {
                return json(['code' => 1, 'msg' => '使用锁定库存失败：' . $e->getMessage()]);
            }
        }

        return json(['code' => 1, 'msg' => '非法请求']);
    }
    
    /**
     * 查看锁定详情
     */
    public function view()
    {
        $id = input('id/d', 0);
        
        if (empty($id)) {
            return $this->error('参数错误');
        }
        
        // 获取锁定记录详情
        $info = Db::name('inventory_lock')
            ->alias('il')
            ->join('product p', 'il.product_id = p.id', 'left')
            ->join('warehouse w', 'il.warehouse_id = w.id', 'left')
            ->join('admin a', 'il.created_by = a.id', 'left')
            ->field('il.*, p.title as product_name, p.material_code, p.unit, p.specs,
                    w.name as warehouse_name, a.name as creator_name')
            ->where('il.id', $id)
            ->find();
        
        if (!$info) {
            return $this->error('锁定记录不存在');
        }
        
        // 获取状态文本
        $statusArr = InventoryLockModel::getStatusArr();
        $info['status_text'] = $statusArr[$info['status']] ?? '未知';
        
        // 获取关联类型文本
        $refTypeArr = InventoryLockModel::getRefTypeArr();
        $info['ref_type_text'] = $refTypeArr[$info['ref_type']] ?? '未知';
        
        View::assign('info', $info);
        
        return View::fetch();
    }
    
    /**
     * 批量释放锁定
     */
    public function batchUnlock()
    {
        if (request()->isPost()) {
            $param = get_params();
            $ids = $param['ids'] ?? '';
            
            if (empty($ids)) {
                return json(['code' => 1, 'msg' => '请选择要释放的锁定记录']);
            }
            
            $idArray = explode(',', $ids);
            
            try {
                $service = new InventoryLockService();
                $successCount = 0;
                $failCount = 0;
                
                foreach ($idArray as $id) {
                    try {
                        $service->releaseLockedInventory([(int)$id]);
                        $successCount++;
                    } catch (\Exception $e) {
                        $failCount++;
                    }
                }
                
                $msg = "批量释放完成，成功：{$successCount}条，失败：{$failCount}条";
                return json(['code' => 0, 'msg' => $msg]);
            } catch (\Exception $e) {
                return json(['code' => 1, 'msg' => '批量释放失败：' . $e->getMessage()]);
            }
        }
        
        return json(['code' => 1, 'msg' => '非法请求']);
    }
    
    /**
     * 根据关联信息获取锁定记录
     */
    public function getLocksByRef()
    {
        $param = get_params();
        $refType = $param['ref_type'] ?? '';
        $refId = $param['ref_id'] ?? 0;
        
        if (empty($refType) || empty($refId)) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        
        try {
            $service = new InventoryLockService();
            $locks = $service->getLocksByRef($refType, $refId);
            
            return json(['code' => 0, 'msg' => '获取成功', 'data' => $locks]);
        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 检查库存是否可锁定
     */
    public function checkLockable()
    {
        $param = get_params();
        $productId = $param['product_id'] ?? 0;
        $warehouseId = $param['warehouse_id'] ?? 0;
        $quantity = $param['quantity'] ?? 0;
        
        if (empty($productId) || empty($warehouseId) || empty($quantity)) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        
        try {
            $service = new InventoryLockService();
            $canLock = $service->hasEnoughStock($productId, $quantity, $warehouseId);
            
            if ($canLock) {
                return json(['code' => 0, 'msg' => '库存充足，可以锁定']);
            } else {
                return json(['code' => 1, 'msg' => '库存不足，无法锁定']);
            }
        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '检查失败：' . $e->getMessage()]);
        }
    }
}
