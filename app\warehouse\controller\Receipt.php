<?php
declare (strict_types = 1);

namespace app\warehouse\controller;
use app\base\BaseController;
use app\warehouse\model\Receipt as ReceiptModel;
use app\warehouse\model\ReceiptDetail as ReceiptDetailModel;
use app\warehouse\model\ReceiptInspectionLog as ReceiptInspectionLogModel;
use app\purchase\model\Order as OrderModel;
use app\purchase\model\OrderDetail as OrderDetailModel;
use app\product\model\Product as ProductModel;
use app\warehouse\model\Inventory as InventoryModel;
use think\facade\View;
use think\facade\Db;
/**
 * 采购收货控制器
 */
class Receipt extends BaseController
{
    /**
     * 收货单列表
     */
    public function index()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $where = [];
          
            
            if (!empty($param['tab'])){ 
                   if($param['tab'] == 1){
                       $where[] = ['status', '=', 0];
                   }elseif($param['tab'] == 2){
                       $where[] = ['status', '=', 1];
                   }
            } 
            
            // 收货单号搜索
            if (!empty($param['receipt_no'])) {
                $where[] = ['receipt_no', 'like', '%' . $param['receipt_no'] . '%'];
            }
            
            // 订单号搜索
            if (!empty($param['order_no'])) {
                $where[] = ['order_no', 'like', '%' . $param['order_no'] . '%'];
            }
            
            // 供应商搜索
            if (!empty($param['supplier_id'])) {
                $where[] = ['supplier_id', '=', $param['supplier_id']];
            }
            
            // 仓库搜索
            if (!empty($param['warehouse_id'])) {
                $where[] = ['warehouse_id', '=', $param['warehouse_id']];
            }
            
            // 状态搜索
            if (isset($param['status']) && $param['status'] !== '') {
                $where[] = ['status', '=', $param['status']];
            }
            
            // 日期范围搜索
            if (!empty($param['date_range'])) {
                $dateRange = explode(' - ', $param['date_range']);
                if (count($dateRange) == 2) {
                    $where[] = ['receipt_date', 'between', [$dateRange[0], $dateRange[1]]];
                }
            } elseif (!empty($param['start_date']) && !empty($param['end_date'])) {
                $where[] = ['receipt_date', 'between', [$param['start_date'], $param['end_date']]];
            }
            
            $limit = intval($param['limit']);
            $page = intval($param['page']);
            $offset = ($page - 1) * $limit;
            
            $count = ReceiptModel::where($where)->count();
            $list = ReceiptModel::with(['supplier', 'warehouse','inspector'])
                ->where($where)
                ->limit($offset, $limit)
                ->order('id', 'desc')
                ->select();
                
            $data = [
                'code' => 0,
                'msg' => '',
                'count' => $count,
                'data' => $list
            ];
            
            return json($data);
        }
        
        // 获取供应商列表
        $suppliers = Db::name('purchase_supplier')
            ->field('id, name')
            ->where('status', 1)
            ->select();
            
        // 获取仓库列表
        $warehouses = Db::name('warehouse')
            ->field('id, name')
            ->where('status', 1)
            ->select();
            
        View::assign('suppliers', $suppliers);
        View::assign('warehouses', $warehouses);
        
        return View::fetch();
    }
    
    /**
     * 添加收货单
     */
    public function add()
    {
        // 添加调试信息
        $requestMethod = request()->method();
        $isAjax = request()->isAjax();
        
        if (request()->isAjax()) {
            $param = get_params();

            // 验证 Token
            try {
                $check = validate()->rule(['__token__' => 'require|token'])->check(['__token__' => $param['__token__'] ?? '']);
                if (!$check) {
                    return json(['code' => 1, 'msg' => '请勿重复提交表单']);
                }
            } catch (\Exception $e) {
                return json(['code' => 1, 'msg' => '请勿重复提交表单：' . $e->getMessage()]);
            }

            // 检查必要参数
            if (empty($param['order_id'])) {
                return json(['code' => 1, 'msg' => '订单ID不能为空']);
            }
            
            if (empty($param['warehouse_id'])) {
                return json(['code' => 1, 'msg' => '仓库不能为空']);
            }
            
            $details = isset($param['details']) ? $param['details'] : [];
            if (empty($details)) {
                return json(['code' => 1, 'msg' => '收货明细不能为空']);
            }
            
            // 开启事务
            Db::startTrans();
            try {
                // 获取订单信息
                $order = OrderModel::find($param['order_id']);
                if (!$order) {
                    return json(['code' => 1, 'msg' => '订单不存在']);
                }
                
                // 只有已审核或部分入库的订单才能创建收货单
                if ($order->status != 3 && $order->status != 4) {
                    return json(['code' => 1, 'msg' => '只有已审核或部分入库的订单才能创建收货单']);
                }
                
                // 生成收货单号
                $receipt_no = $this->generateReceiptNo();
                
                // 创建收货单
                $receipt = new ReceiptModel();
                $receipt->receipt_no = $receipt_no;
                $receipt->order_id = $order->id;
                $receipt->order_no = $order->order_no;
                $receipt->supplier_id = $order->supplier_id;
                $receipt->warehouse_id = $param['warehouse_id'];
                $receipt->receipt_date = $param['receipt_date'] ?: date('Y-m-d');
                $receipt->supplier_delivery_no = $param['supplier_delivery_no'] ?: '';
                $receipt->total_amount = 0; // 暂时设为0，后续更新
                $receipt->status = 0; // 草稿状态
                $receipt->notes = $param['notes'] ?: '';
                $receipt->created_by = $this->uid;
                $receipt->inspection_status = ReceiptModel::INSPECTION_PENDING; // 设置初始质检状态为未检验
                $receipt->save();
                
                // 获取订单明细
                $orderDetails = OrderDetailModel::where('order_id', $order->id)->select();
                $orderDetailsMap = [];
                foreach ($orderDetails as $od) {
                    $orderDetailsMap[$od->id] = $od;
                }
                
                // 创建收货单明细
                $totalAmount = 0;
                $validOrderDetailIds = [];
                
                foreach ($details as $detail) {
                    // 找到对应的订单明细
                    if (!isset($orderDetailsMap[$detail['order_detail_id']])) {
                        continue;
                    }
                    
                    $orderDetail = $orderDetailsMap[$detail['order_detail_id']];
                    
                    // 获取产品信息
                    $product = ProductModel::find($detail['product_id']);
                    if (!$product) {
                        continue;
                    }
                    
                    // 创建收货单明细
                    $receiptDetail = new ReceiptDetailModel();
                    $receiptDetail->receipt_id = $receipt->id;
                    $receiptDetail->order_detail_id = $orderDetail->id;
                    $receiptDetail->product_id = $product->id;
                    $receiptDetail->quantity = $detail['quantity'];
                    $receiptDetail->unit = $product->unit;
                    $receiptDetail->unit_price = $orderDetail->unit_price;
                    $receiptDetail->total_amount = $detail['quantity'] * $orderDetail->unit_price;
                    $receiptDetail->batch_no = $detail['batch_no'] ?: '';
                    $receiptDetail->notes = $detail['notes'] ?: '';
                    $receiptDetail->save();
                    
                    $totalAmount += $receiptDetail->total_amount;
                    $validOrderDetailIds[] = $orderDetail->id;
                }
                
                // 更新收货单总金额
                $receipt->total_amount = $totalAmount;
                $receipt->save();
                
                Db::commit();
                return json(['code' => 0, 'msg' => '添加成功', 'data' => ['id' => $receipt->id]]);
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 1, 'msg' => '添加失败：' . $e->getMessage()]);
            }
        }
        
        // 获取订单ID
        $orderId = input('order_id', 0);
        if (empty($orderId)) {
            $this->error('参数错误');
        }
        
        // 获取订单信息
        $order = OrderModel::with(['supplier', 'details.product'])->find($orderId);
        if (!$order) {
            $this->error('订单不存在');
        }
        
        // 获取仓库列表
        $warehouses = Db::name('warehouse')
            ->field('id, name')
            ->where('status', 1)
            ->select();
        
        View::assign([
            'order' => $order,
            'warehouses' => $warehouses
        ]);
        
        // 记录请求方法和参数
        $debug = [
            'request_method' => request()->method(),
            'is_ajax' => request()->isAjax(),
            'order_id' => $orderId
        ];
        View::assign('debug', $debug);
        
        return View::fetch();
    }
    
    /**
     * 编辑收货单
     */
    public function edit()
    {
        if (request()->isAjax()) {
            $param = get_params();
            
            // 检查必要参数
            if (empty($param['id'])) {
                return json(['code' => 1, 'msg' => '收货单ID不能为空']); 
            }
            
            if (empty($param['warehouse_id'])) {
                return json(['code' => 1, 'msg' => '仓库不能为空']);    
            }
            
            // 构建收货明细数组
            $details = [];
            
            if (isset($param['detail_ids']) && is_array($param['detail_ids'])) {
                $count = count($param['detail_ids']);
                for ($i = 0; $i < $count; $i++) {
                    if (isset($param['quantities'][$i]) && floatval($param['quantities'][$i]) > 0) {
                        $details[] = [
                            'detail_id' => $param['detail_ids'][$i],
                            'product_id' => $param['product_ids'][$i],
                            'order_detail_id' => $param['order_detail_ids'][$i],
                            'quantity' => $param['quantities'][$i]
                        ];
                    }
                }
            }
            
            if (empty($details)) {
                return json(['code' => 1, 'msg' => '收货明细不能为空']);
            }
            
            
            // 开启事务
            Db::startTrans();
            try {
                // 获取收货单
                $receipt = ReceiptModel::find($param['id']);
                if (!$receipt) {
                    return to_assign(1, '收货单不存在');
                }
                
                // 只有草稿状态的收货单才能编辑
                if ($receipt->status != 0) {
                    return json(['code' => 1, 'msg' => '只有草稿状态的收货单才能编辑']);
                }
                
                // 更新收货单
                $receipt->warehouse_id = $param['warehouse_id'];
                $receipt->receipt_date = $param['receipt_date'] ?: date('Y-m-d');
                $receipt->supplier_delivery_no = $param['supplier_delivery_no'] ?: '';
                $receipt->notes = $param['notes'] ?: '';
                $receipt->updated_by = $this->uid;
                
                // 获取订单明细
                $order = OrderModel::find($receipt->order_id);
                $orderDetails = OrderDetailModel::where('order_id', $order->id)->select();
                $orderDetailsMap = [];
                foreach ($orderDetails as $od) {
                    $orderDetailsMap[$od->id] = $od;
                }
                
                // 删除原来的收货单明细
                ReceiptDetailModel::where('receipt_id', $receipt->id)->delete();
                
                // 重新创建收货单明细
                $totalAmount = 0;
                $validOrderDetailIds = [];
                
                foreach ($details as $detail) {
                    // 找到对应的订单明细
                    if (!isset($orderDetailsMap[$detail['order_detail_id']])) {
                        continue;
                    }
                    
                    $orderDetail = $orderDetailsMap[$detail['order_detail_id']];
                    
                    // 获取产品信息
                    $product = ProductModel::find($detail['product_id']);
                    if (!$product) {
                        continue;
                    }
                    
                    // 计算未收货数量
                    $unreceived_quantity = $orderDetail->quantity - $orderDetail->received_quantity;
                    
                    // 检查收货数量是否超过未收货数量
                    $receipt_quantity = floatval($detail['quantity']);
                    if ($receipt_quantity > $unreceived_quantity) {
                        throw new \Exception("产品 {$product->name} 的收货数量不能超过未收货数量");
                    }
                    
                    // 创建收货单明细
                    $receiptDetail = new ReceiptDetailModel();
                    $receiptDetail->receipt_id = $receipt->id;
                    $receiptDetail->order_detail_id = $detail['order_detail_id'];
                    $receiptDetail->product_id = $detail['product_id'];
                    $receiptDetail->product_code = $product->material_code;
                    $receiptDetail->product_name = $product->title;
                    $receiptDetail->specs = $product->specs;
                    $receiptDetail->unit = $product->unit;
                    $receiptDetail->quantity = $receipt_quantity;
                    $receiptDetail->price = $orderDetail->price;
                    $receiptDetail->amount = $receipt_quantity * $orderDetail->price;
                    $receiptDetail->quality_status = 1; // 默认合格
                    $receiptDetail->save();
                    
                    $totalAmount += $receiptDetail->amount;
                    $validOrderDetailIds[] = $detail['order_detail_id'];
                }
                
                // 更新收货单总金额
                $receipt->total_amount = $totalAmount;
                $receipt->save();
                
                Db::commit();
                return json(['code' => 0, 'msg' => '收货单更新成功']);
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 1, 'msg' => '收货单更新失败：' . $e->getMessage()]); 
            }
        }
        
        $id = input('id', 0, 'intval');
        if ($id <= 0) {
            $this->error('参数错误');
        }
        
        // 获取收货单信息
        $receipt = ReceiptModel::with(['details.product', 'supplier', 'warehouse', 'order.details.product', 'creator'])
            ->where('id', $id)
            ->find();

           
            
        if (!$receipt) {
            $this->error('收货单不存在');
        }
        
        // 只有草稿状态的收货单才能编辑
        if ($receipt->status != 0) {
            $this->error('只有草稿状态的收货单才能编辑');
        }
        
        // 获取仓库列表
        $warehouses = Db::name('warehouse')
            ->field('id, name')
            ->where('status', 1)
            ->select();
            
        View::assign('receipt', $receipt);
        View::assign('warehouses', $warehouses);
        
        return View::fetch();
    }
    
    /**
     * 收货单详情
     */
    public function detail()
    {
        $id = input('id', 0, 'intval');
        if ($id <= 0) {
            $this->error('参数错误');
        }
        
        // 获取收货单信息
        $receipt = ReceiptModel::with(['details.product', 'supplier', 'warehouse', 'order.details.product', 'creator'])
            ->where('id', $id)
            ->find();
            
            
        if (!$receipt) {
            $this->error('收货单不存在');
        }
        
        // 检查和处理关联数据缺失的情况
        if (!$receipt->supplier) {
            $receipt->supplier = ['name' => '未知供应商'];
        }
        
        if (!$receipt->warehouse) {
            $receipt->warehouse = ['name' => '未知仓库'];
        }
        
        if (!isset($receipt->creator) || !$receipt->creator) {
            $receipt->creator = ['realname' => '未知'];
        }
        
        // 获取每个收货明细对应的检验单信息
        if ($receipt->details) {
            foreach ($receipt->details as $detail) {
                // 查找该收货明细对应的检验单
                $inspectOrder = Db::table('oa_inspect_order')
                    ->where('source_type', 1) // 来料检验
                    ->where('source_id', $detail->id)
                    ->find();
                
                $detail->inspect_order = $inspectOrder;
            }
        }
        
         //print_r($receipt['details']);
       
        View::assign('receipt', $receipt);
        
        return View::fetch();
    }
    
    /**
     * 删除收货单
     */
    public function delete()
    {
        if (request()->isAjax()) {
            $id = input('id', 0, 'intval');
            if ($id <= 0) {
                return json(['code' => 1, 'msg' => '参数错误']);
            }
            
            // 获取收货单
            $receipt = ReceiptModel::find($id);
            if (!$receipt) {
                return json(['code' => 1, 'msg' => '收货单不存在']);
            }
            
            // 只有草稿状态的收货单才能删除
            if ($receipt->status != 0) {
                return json(['code' => 1, 'msg' => '只有草稿状态的收货单才能删除']);
            }
            
            // 开启事务
            Db::startTrans();
            try {
                // 删除收货单明细
                ReceiptDetailModel::where('receipt_id', $id)->delete();

                
                // 删除收货单
                $receipt->delete();
                
                Db::commit();
                return json(['code' => 0, 'msg' => '收货单删除成功']);  
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 1, 'msg' => '收货单删除失败：' . $e->getMessage()]);
            }
        }
        
        return to_assign(1, '非法请求');
    }
    
    /**
     * 提交收货单
     */
    public function submit()
    {

        if (request()->isAjax()) {
            $id = input('id', 0, 'intval');
            if ($id <= 0) {
                return json(['code' => 1, 'msg' => '参数错误']);
            }
            
            // 获取收货单
            $receipt = ReceiptModel::with('details')
                ->where('id', $id)
                ->find();
                
            if (!$receipt) {
                return json(['code' => 1, 'msg' => '收货单不存在']);    

            }
            
            // 只有草稿状态的收货单才能提交
            if ($receipt->status != 0) {
                return json(['code' => 1, 'msg' => '只有草稿状态的收货单才能提交']);
            }
            
            // 检查质检状态，只有质检合格的收货单才能入库
            // if ($receipt->inspection_status != ReceiptModel::INSPECTION_PASSED) {
            //     return json(['code' => 1, 'msg' => '只有质检合格的收货单才能入库']);
            // }
            
            // 检查收货单明细
            if ($receipt->details->isEmpty()) {
                return json(['code' => 1, 'msg' => '收货单明细不能为空']);           
            }

            // 检查是否有合格的产品可以入库
            // $hasQualifiedProducts = false;
            // foreach ($receipt->details as $detail) {
            //     if ($detail->quality_status == 2) { // 质检合格
            //         $hasQualifiedProducts = true;
            //         break;
            //     }
            // }
            
            // if (!$hasQualifiedProducts) {
            //     return json(['code' => 1, 'msg' => '没有质检合格的产品，无法入库']);
            // }
            
            // 开启事务
            Db::startTrans();
            try {
                // 更新收货单状态
                $receipt->status = 1; // 已提交/已入库
                $receipt->submitted_at = time();
                $receipt->submitted_by = $this->uid;
                $receipt->save();
                
                // 获取订单
                $order = OrderModel::find($receipt->order_id);
                if (!$order) {
                    throw new \Exception('采购订单不存在');
                }
               
                // 获取订单明细
                $orderDetails = OrderDetailModel::where('order_id', $order->id)->select();
                $orderDetailsMap = [];
                foreach ($orderDetails as $od) {
                    $orderDetailsMap[$od->id] = $od;
                }
               
                // 处理库存和订单明细更新
                foreach ($receipt->details as $detail) {
                    // 只处理质检合格的产品
                  
                        // 更新订单明细的已收货数量
                        if (isset($orderDetailsMap[$detail->order_detail_id])) {
                            $orderDetail = $orderDetailsMap[$detail->order_detail_id];
                            $orderDetail->received_quantity += $detail->quantity;   
                            $orderDetail->save();
                        }
                        
                        // 检查是否存在库存记录
                        $inventory = InventoryModel::where([
                            'product_id' => $detail->product_id,
                            'warehouse_id' => $receipt->warehouse_id,
                            'batch_no' => $detail->batch_no ?: null
                        ])->find();
                        
                        $beforeQuantity = 0;
                        if ($inventory) {
                            // 更新库存
                            $beforeQuantity = $inventory->quantity;
                            $inventory->quantity += $detail->quantity;
                            $inventory->available_quantity += $detail->quantity;
                            $inventory->save();
                            
                            \think\facade\Log::info('更新现有库存', [
                                'inventory_id' => $inventory->id,
                                'before_quantity' => $beforeQuantity,
                                'after_quantity' => $inventory->quantity,
                                'product_id' => $detail->product_id,
                                'warehouse_id' => $receipt->warehouse_id
                            ]);
                        } else {
                            // 创建新库存记录
                            $inventory = new InventoryModel();
                            $inventory->product_id = $detail->product_id;
                            $inventory->warehouse_id = $receipt->warehouse_id;
                            $inventory->batch_no = $detail->batch_no ?: null;
                            $inventory->quantity = $detail->quantity;
                            $inventory->available_quantity = $detail->quantity;
                            $inventory->allocated_quantity = 0;
                            $inventory->unit = $detail->unit;
                            $inventory->cost_price = $detail->price;    
                            $inventory->receipt_id = $receipt->id;
                            $inventory->save();
                            
                            \think\facade\Log::info('创建新库存记录', [
                                'inventory_id' => $inventory->id,
                                'quantity' => $inventory->quantity,
                                'product_id' => $detail->product_id,
                                'warehouse_id' => $receipt->warehouse_id
                            ]);
                        }
                        
                        // 记录库存变动日志
                        $this->recordInventoryLog(
                            $detail->product_id,
                            $receipt->warehouse_id,
                            $detail->batch_no,
                            1, // 入库
                            $detail->quantity,
                            $beforeQuantity,
                            $beforeQuantity + $detail->quantity,
                            $detail->unit,
                            'purchase_receipt',
                            $receipt->id,
                            $receipt->receipt_no,
                            '采购入库: ' . $receipt->receipt_no
                        );
                    
                }
                
                // 更新订单状态（只考虑合格品的收货数量）
                $allReceived = true;
                foreach ($orderDetails as $od) {
                    // 获取该订单明细下所有合格品的收货数量
                    $qualifiedReceivedQuantity = Db::name('purchase_receipt_detail')
                        ->alias('rd')
                        ->join('purchase_receipt r', 'r.id = rd.receipt_id')
                        ->where([
                            'rd.order_detail_id' => $od->id,
                            'rd.quality_status' => 2, // 只统计合格品
                            'r.status' => 1 // 已入库的收货单
                        ])
                        ->sum('rd.quantity');
                    
                    if ($qualifiedReceivedQuantity < $od->quantity) {
                        $allReceived = false;
                        break;
                    }
                }
                
                if ($allReceived) {
                    $order->status = 5; // 已完成
                } else {
                    $order->status = 4; // 部分入库
                }
                $order->save();
                
                // 入库完成后，触发自动分配
                $this->triggerAutoAllocation($receipt);

                Db::commit();
                return json(['code' => 0, 'msg' => '收货单提交成功']);
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 1, 'msg' => '收货单提交失败：' . $e->getMessage()]);
            }
        }
        
        return to_assign(1, '非法请求');
    }

    /**
     * 触发入库后的自动分配
     */
    private function triggerAutoAllocation($receipt)
    {
        try {
            $allocationService = new \app\warehouse\service\InventoryAllocationService();

            // 按产品分组统计入库数量
            $inboundProducts = [];
            foreach ($receipt->details as $detail) {
                $key = $detail->product_id . '_' . $receipt->warehouse_id;
                if (!isset($inboundProducts[$key])) {
                    $inboundProducts[$key] = [
                        'product_id' => $detail->product_id,
                        'warehouse_id' => $receipt->warehouse_id,
                        'quantity' => 0
                    ];
                }
                $inboundProducts[$key]['quantity'] += $detail->quantity;
            }

            // 对每个产品触发自动分配
            foreach ($inboundProducts as $product) {
                $result = $allocationService->autoAllocateOnInbound(
                    $product['product_id'],
                    $product['warehouse_id'],
                    $product['quantity']
                );

                // 记录分配结果日志
                if ($result['code'] == 0 && $result['total_allocated'] > 0) {
                    \think\facade\Log::info('入库自动分配成功', [
                        'receipt_id' => $receipt->id,
                        'product_id' => $product['product_id'],
                        'warehouse_id' => $product['warehouse_id'],
                        'inbound_quantity' => $product['quantity'],
                        'allocated_quantity' => $result['total_allocated'],
                        'allocation_details' => $result['allocation_details']
                    ]);

                    // 发送通知给相关业务模块
                    $this->notifyAllocationComplete($result);
                }
            }

        } catch (\Exception $e) {
            // 自动分配失败不影响入库流程，只记录日志
            \think\facade\Log::error('入库自动分配失败', [
                'receipt_id' => $receipt->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 通知分配完成
     */
    private function notifyAllocationComplete($allocationResult)
    {
        try {
            // 这里可以实现通知逻辑，比如：
            // 1. 发送邮件通知
            // 2. 系统消息通知
            // 3. 更新相关业务状态

            foreach ($allocationResult['allocation_details'] as $detail) {
                if ($detail['code'] == 0) {
                    // 根据业务类型发送不同的通知
                    switch ($detail['ref_type']) {
                        case 'customer_order':
                            $this->notifyCustomerOrderAllocation($detail);
                            break;
                        case 'production_order':
                            $this->notifyProductionOrderAllocation($detail);
                            break;
                    }
                }
            }

        } catch (\Exception $e) {
            \think\facade\Log::error('分配通知发送失败', [
                'error' => $e->getMessage(),
                'allocation_result' => $allocationResult
            ]);
        }
    }

    /**
     * 通知客户订单分配完成
     */
    private function notifyCustomerOrderAllocation($detail)
    {
        // 检查订单是否完全分配，如果是则更新订单状态
        $orderId = $detail['ref_id'];
        $pendingRequests = \think\Db::name('inventory_allocation_request')
            ->where('ref_type', 'customer_order')
            ->where('ref_id', $orderId)
            ->where('status', 'in', [1, 2]) // 待分配和部分分配
            ->count();

        if ($pendingRequests == 0) {
            // 所有商品都已分配完成，更新订单状态为已审核
            \think\Db::name('order')
                ->where('id', $orderId)
                ->where('status', 3) // 只更新待补货状态的订单
                ->update([
                    'status' => 1, // 已审核
                    'update_time' => time()
                ]);

            \think\facade\Log::info('客户订单库存分配完成', [
                'order_id' => $orderId,
                'ref_no' => $detail['ref_no']
            ]);
        }
    }

    /**
     * 通知生产订单分配完成
     */
    private function notifyProductionOrderAllocation($detail)
    {
        // 生产订单的通知逻辑
        \think\facade\Log::info('生产订单原料分配完成', [
            'production_order_id' => $detail['ref_id'],
            'ref_no' => $detail['ref_no'],
            'allocated_quantity' => $detail['allocated_quantity']
        ]);
    }
    
    /**
     * 反审核收货单
     */
    public function unapprove()
    {
        if (request()->isAjax()) {
            $id = input('id', 0, 'intval');
            if ($id <= 0) {
                return json(['code' => 1, 'msg' => '参数错误']);
            }
            
            // 获取收货单
            $receipt = ReceiptModel::with('details')
                ->where('id', $id)
                ->find();
                
            if (!$receipt) {
                return json(['code' => 1, 'msg' => '收货单不存在']);
            }
            
            // 只有已提交/已入库状态的收货单才能反审核
            if ($receipt->status != 1) {
                return json(['code' => 1, 'msg' => '只有已入库状态的收货单才能反审核']);
            }
            
            // 开启事务
            Db::startTrans();
            try {
                // 更新收货单状态  inventory_id
                $receipt->status = 0; // 草稿状态
                $receipt->save();
                
                // 获取订单
                $order = OrderModel::find($receipt->order_id);
                if (!$order) {
                    throw new \Exception('采购订单不存在');
                }
                
                // 获取订单明细
                $orderDetails = OrderDetailModel::where('order_id', $order->id)->select();
                $orderDetailsMap = [];
                foreach ($orderDetails as $od) {
                    $orderDetailsMap[$od->id] = $od;
                }
                
                // 处理库存和订单明细更新
                foreach ($receipt->details as $detail) {
                    // 更新订单明细的已收货数量
                    if (isset($orderDetailsMap[$detail->order_detail_id])) {
                        $orderDetail = $orderDetailsMap[$detail->order_detail_id];
                        $orderDetail->received_quantity -= $detail->quantity;
                        if ($orderDetail->received_quantity < 0) {
                            $orderDetail->received_quantity = 0;
                        }
                        $orderDetail->save();
                    }
                    
                    // 更新库存（只有合格品才入库）
                    if ($detail->quality_status == ReceiptDetailModel::QUALITY_GOOD) {
                        // 检查是否存在库存记录
                        $inventory = InventoryModel::where([
                            'product_id' => $detail->product_id,
                            'warehouse_id' => $receipt->warehouse_id,
                           // 'receipt_id' => $detail->receipt_id
                            'batch_no' => $detail->batch_no ?: null
                        ])->find();
                        
                        if (!$inventory) {
                            throw new \Exception('产品库存记录不存在，无法反审核');
                        }

                        
                        // 检查是否存在已出库的预占记录 reservedQuantity
                        // $activeReservations = Db::name('inventory_reserve')
                        //     ->where([
                        //         'inventory_id' => $inventory->id,
                        //         'receipt_id' => $receipt->id,
                        //         'status' => 2
                        //     ])
                        //     ->count();
 
                        // if ($activeReservations > 0) {
                        //     throw new \Exception('预占已经出库，无法反审了！');
                        // }

                        // 获取需要删除的预占记录的总数量（只计算当前收货单的预占）
                        // $reservedQuantity = Db::name('inventory_reserve')
                        //     ->where([
                        //         'inventory_id' => $inventory->id,
                        //         'receipt_id' => $receipt->id,
                        //         'status' => [0, 1]
                        //     ])
                        //     ->sum('quantity');

                        // 删除预占记录（只删除当前收货单的预占）
                        // Db::name('inventory_reserve')
                        //     ->where([
                        //         'inventory_id' => $inventory->id,
                        //         'receipt_id' => $receipt->id,
                        //         'status' => [0, 1]
                        //     ])
                        //     ->delete();

                        // 恢复库存可用数量
                        // if ($reservedQuantity > 0) {
                        //     $inventory->available_quantity += $reservedQuantity;
                        //     $inventory->reserved_quantity -= $reservedQuantity;
                        //     $inventory->save();
                        // }
                        
                        // 检查库存是否足够
                        // if ($inventory->quantity < $detail->quantity) {
                        //     throw new \Exception('产品库存不足，无法反审核');
                        // }
                        
                        // if ($inventory->available_quantity < $detail->quantity) {
                        //     throw new \Exception('产品可用库存不足，无法反审核');
                        // }
                        



                        

                        // 更新库存
                        $beforeQuantity = $inventory->quantity;
                        $inventory->quantity -= $detail->quantity;
                        $inventory->available_quantity -= $detail->quantity;
                        $inventory->save();
                        
                        // 记录库存变动日志
                        // $this->recordInventoryLog(
                        //     $detail->product_id,
                        //     $receipt->warehouse_id,
                        //     $detail->batch_no,
                        //     2, // 出库
                        //     -$detail->quantity,
                        //     $beforeQuantity,
                        //     $beforeQuantity - $detail->quantity,
                        //     $detail->unit,
                        //     'purchase_receipt',
                        //     $receipt->id,
                        //     $receipt->receipt_no,
                        //     '采购反审核: ' . $receipt->receipt_no
                        // );
                        //删除库存记录
                        db::name('inventory_log')->where('related_bill_id', $receipt->id)->delete();
                        //恢复质检记录 
                        $receipt->inspection_status = 0;
                        $receipt->save();
                        //质检明细也需要处理
                        $receipt->details->each(function($detail) {
                            $detail->quality_status = 0;
                            $detail->save();
                        });

                        
                        
                    }
                }
                
                // 检查是否还有其他针对同一订单的已入库收货单
                $otherReceipts = ReceiptModel::where('order_id', $receipt->order_id)
                    ->where('id', '<>', $receipt->id)
                    ->where('status', 1) // 已入库状态
                    ->count();
                
                // 更新订单状态
                if ($otherReceipts > 0) {
                    // 仍有其他入库记录，检查订单明细是否全部收货完成
                    $allReceived = true;
                    foreach ($orderDetails as $od) {
                        if ($od->received_quantity < $od->quantity) {
                            $allReceived = false;
                            break;
                        }
                    }
                    
                    if ($allReceived) {
                        $order->status = 5; // 已完成
                    } else {
                        $order->status = 4; // 部分入库
                    }
                } else {
                    // 没有其他入库记录，检查是否有任何收货数量
                    $anyReceived = false;
                    foreach ($orderDetails as $od) {
                        if ($od->received_quantity > 0) {
                            $anyReceived = true;
                            break;
                        }
                    }
                    
                    if ($anyReceived) {
                        $order->status = 4; // 部分入库
                    } else {
                        $order->status = 3; // 已审核，无入库记录
                    }
                }
                $order->save();
                
                Db::commit();
                return json(['code' => 0, 'msg' => '收货单反审核成功']);
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 1, 'msg' => '收货单反审核失败：' . $e->getMessage()]);   
            }
        }
        
        return to_assign(1, '非法请求');
    }
    
    /**
     * 生成收货单号
     */
    private function generateReceiptNo()
    {
        $prefix = 'GR';
        $date = date('Ymd');
        
        // 先查询数据库中所有以当天日期开头的收货单号
        $maxNo = ReceiptModel::where('receipt_no', 'like', $prefix . $date . '%')
            ->max('receipt_no');
        
        $increment = 1;
        
        if ($maxNo) {
            // 当天存在单号，在当天最大序号基础上+1
            $increment = intval(substr($maxNo, -4)) + 1;
        } else {
            // 当天不存在单号，则从1开始编号
            $increment = 1;
        }
        
        // 构建新单号
        $newReceiptNo = $prefix . $date . str_pad((string)$increment, 4, '0', STR_PAD_LEFT);
        
        // 检查新单号是否已存在（可能是被删除又恢复的数据）
        while (ReceiptModel::where('receipt_no', $newReceiptNo)->count() > 0) {
            $increment++;
            $newReceiptNo = $prefix . $date . str_pad((string)$increment, 4, '0', STR_PAD_LEFT);
        }
        
        // 返回确保唯一的单号
        return $newReceiptNo;
    }




    
    /**
     * 记录库存日志
     */
    private function recordInventoryLog($productId, $warehouseId, $batchNo, $type, $quantity, $beforeQuantity, $afterQuantity, $unit, $billType, $billId, $billNo, $notes)
    {
        Db::name('inventory_log')->insert([
            'product_id' => $productId,
            'warehouse_id' => $warehouseId,
            'batch_no' => $batchNo ?: null,
            'type' => $type,
            'quantity' => $quantity,
            'before_quantity' => $beforeQuantity,
            'after_quantity' => $afterQuantity,
            'unit' => $unit,
            'related_bill_type' => $billType,
            'related_bill_id' => $billId,
            'related_bill_no' => $billNo,
            'notes' => $notes,
            'operation_by' => $this->uid,
            'operation_time' => time(),
            'create_time' => time()
        ]);
    }

    /**
     * 待收货采购订单列表
     * 显示所有通过审核但未完全入库的采购订单
     */
    public function pendingOrders()
    {
        if (request()->isAjax()) {
            $param = get_params();
            
 


            $where[] = ['check_status', '=', 2];
            $where[] = ['status', 'in', [3, 4]]; // 状态2:审核 3:部分入库, 4:部分入库
         
            

            // 订单号搜索
            if (!empty($param['order_no'])) {
                $where[] = ['order_no', 'like', '%' . $param['order_no'] . '%'];
            }
            
            // 供应商搜索
            if (!empty($param['supplier_id'])) {
                $where[] = ['supplier_id', '=', $param['supplier_id']];
            }
            
            // 日期范围搜索
            if (!empty($param['date_range'])) {
                $dateRange = explode(' - ', $param['date_range']);
                if (count($dateRange) == 2) {
                    $where[] = ['order_date', 'between', [$dateRange[0], $dateRange[1]]];
                }
            }
            
            $limit = intval($param['limit']);
            $page = intval($param['page']);
            $offset = ($page - 1) * $limit;
            
            $count = OrderModel::where($where)->count();
            $list = OrderModel::with(['supplier'])
                ->where($where)
                ->limit($offset, $limit)
                ->order('id', 'desc')
                ->select();
                
            // 计算每个订单的收货情况
            foreach ($list as &$order) {
                // 获取订单明细
                $details = OrderDetailModel::where('order_id', $order->id)->select();
                $total_quantity = 0;
                $received_quantity = 0;
                
                foreach ($details as $detail) {
                    $total_quantity += $detail->quantity;
                    $received_quantity += $detail->received_quantity;
                }
                
                // 计算收货进度百分比
                $order->progress = $total_quantity > 0 ? round(($received_quantity / $total_quantity) * 100) : 0;
                $order->detail_count = count($details);
            }
            
            $data = [
                'code' => 0,
                'msg' => '',
                'count' => $count,
                'data' => $list
            ];
            
            return json($data);
        }
        
        // 获取供应商列表
        $suppliers = Db::name('purchase_supplier')
            ->field('id, name')
            ->where('status', 1)
            ->select();
            
        View::assign('suppliers', $suppliers);
        
        return View::fetch();
    }

    /**
     * 提交质检
     */
    public function submitInspection()
    {
        if (!request()->isAjax()) {
            $this->error('非法请求');
        }
        
        $param = get_params();
        
        // 验证参数
        if (empty($param['id'])) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        
        // 获取收货单
        $receipt = ReceiptModel::find($param['id']);
        if (!$receipt) {
            return json(['code' => 1, 'msg' => '收货单不存在']);
        }
        
        // 检查是否可以质检
        if (!$receipt->canInspect()) {
            return json(['code' => 1, 'msg' => '当前状态不能提交质检']);
        }
        
        Db::startTrans();
        try {
            $oldStatus = $receipt->inspection_status;
            
            // 更新质检状态
            $receipt->inspection_status = ReceiptModel::INSPECTION_PROCESSING;
            $receipt->inspection_by = $this->uid;
            $receipt->inspection_time = time();
            $receipt->inspection_notes = $param['inspection_notes'] ?? '';
            $receipt->save();
            
            // 记录质检日志
            $log = new ReceiptInspectionLogModel;
            $log->receipt_id = $receipt->id;
            $log->inspection_by = $this->uid;
            $log->inspection_time = time();
            $log->old_status = $oldStatus;
            $log->new_status = ReceiptModel::INSPECTION_PROCESSING;
            $log->notes = $param['inspection_notes'] ?? '';
            $log->save();
            
            Db::commit();
            return json(['code' => 0, 'msg' => '提交质检成功']);
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 1, 'msg' => '提交质检失败：' . $e->getMessage()]);
        }
    }

    /**
     * 质检通过
     */
    public function approveInspection()
    {
        if (!request()->isAjax()) {
            $this->error('非法请求');
        }
        
        $param = get_params();
        
        // 验证参数
        if (empty($param['id'])) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        
        // 获取收货单
        $receipt = ReceiptModel::find($param['id']);
        if (!$receipt) {
            return json(['code' => 1, 'msg' => '收货单不存在']);
        }
        
        // 检查是否在质检中
        if ($receipt->inspection_status !== ReceiptModel::INSPECTION_PROCESSING) {
            return json(['code' => 1, 'msg' => '只有质检中的收货单才能审核']);
        }
        
        Db::startTrans();
        try {
            $oldStatus = $receipt->inspection_status;
            
            // 更新质检状态
            $receipt->inspection_status = ReceiptModel::INSPECTION_PASSED;
            $receipt->inspection_notes = $param['inspection_notes'] ?? '';
            $receipt->save();
            
            // 记录质检日志
            $log = new ReceiptInspectionLogModel;
            $log->receipt_id = $receipt->id;
            $log->inspection_by = $this->uid;
            $log->inspection_time = time();
            $log->old_status = $oldStatus;
            $log->new_status = ReceiptModel::INSPECTION_PASSED;
            $log->notes = $param['inspection_notes'] ?? '';
            $log->save();
            
            Db::commit();
            return json(['code' => 0, 'msg' => '质检通过成功']);
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 1, 'msg' => '质检通过失败：' . $e->getMessage()]);
        }
    }

    /**
     * 质检不合格
     */
    public function rejectInspection()
    {
        if (!request()->isAjax()) {
            $this->error('非法请求');
        }
        
        $param = get_params();
        
        // 验证参数
        if (empty($param['id'])) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        
        if (empty($param['inspection_notes'])) {
            return json(['code' => 1, 'msg' => '请填写不合格原因']);
        }
        
        // 获取收货单
        $receipt = ReceiptModel::find($param['id']);
        if (!$receipt) {
            return json(['code' => 1, 'msg' => '收货单不存在']);
        }
        
        // 检查是否在质检中
        if ($receipt->inspection_status !== ReceiptModel::INSPECTION_PROCESSING) {
            return json(['code' => 1, 'msg' => '只有质检中的收货单才能审核']);
        }
        
        Db::startTrans();
        try {
            $oldStatus = $receipt->inspection_status;
            
            // 更新质检状态
            $receipt->inspection_status = ReceiptModel::INSPECTION_FAILED;
            $receipt->inspection_notes = $param['inspection_notes'];
            $receipt->save();
            
            // 记录质检日志
            $log = new ReceiptInspectionLogModel;
            $log->receipt_id = $receipt->id;
            $log->inspection_by = $this->uid;
            $log->inspection_time = time();
            $log->old_status = $oldStatus;
            $log->new_status = ReceiptModel::INSPECTION_FAILED;
            $log->notes = $param['inspection_notes'];
            $log->save();
            
            Db::commit();
            return json(['code' => 0, 'msg' => '质检不合格操作成功']);
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 1, 'msg' => '质检不合格操作失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 完成详细质检
     */
    public function completeInspection()
    {
        if (!request()->isAjax()) {
            $this->error('非法请求');
        }
        
        $param = get_params();
        
        // 验证参数
        if (empty($param['id'])) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        
        // 验证 Token
        try {
            $check = validate()->rule(['__token__' => 'require|token'])->check(['__token__' => $param['__token__'] ?? '']);
            if (!$check) {
                return json(['code' => 1, 'msg' => '请勿重复提交表单']);
            }
        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '请勿重复提交表单：' . $e->getMessage()]);
        }
        
        // 获取收货单
        $receipt = ReceiptModel::find($param['id']);
        if (!$receipt) {
            return json(['code' => 1, 'msg' => '收货单不存在']);
        }
        
        // 检查是否可以质检
        if (!$receipt->canInspect()) {
            return json(['code' => 1, 'msg' => '当前状态不能进行质检']);
        }
        
        Db::startTrans();
        try {
            $oldStatus = $receipt->inspection_status;
            
            // 更新质检状态
            $receipt->inspection_status = intval($param['quality_status']); // 2=合格，3=不合格
            $receipt->inspection_by = $this->uid;
            $receipt->inspection_time = time();
            $receipt->inspection_notes = $param['notes'] ?? '';
            $receipt->save();
            
            // 记录质检日志
            $log = new ReceiptInspectionLogModel;
            $log->receipt_id = $receipt->id;
            $log->inspection_by = $this->uid;
            $log->inspection_time = time();
            $log->old_status = $oldStatus;
            $log->new_status = intval($param['quality_status']);
            $log->notes = $param['notes'] ?? '';
            $log->save();
            
            // 处理质检明细
            if (!empty($param['details']) && is_array($param['details'])) {
                foreach ($param['details'] as $detailData) {
                    if (empty($detailData['detail_id'])) {
                        continue;
                    }
                    
                    // 获取收货单明细
                    $detail = Db::name('purchase_receipt_detail')->where('id', $detailData['detail_id'])->find();
                    if (!$detail) {
                        continue;
                    }
                    
                    $oldDetailStatus = $detail['quality_status'] ?? 1; // 默认为待检
                    
                    // 更新质检信息
                    Db::name('purchase_receipt_detail')->where('id', $detailData['detail_id'])->update([
                        'quality_status' => intval($detailData['quality_status']),
                        'abnormal_reason' => $detailData['abnormal_reason'] ?? '',
                        'notes' => $detailData['notes'] ?? '',
                        'update_time' => time()
                    ]);
                    
                    // 记录明细质检日志
                    $detailLog = new ReceiptInspectionLogModel;
                    $detailLog->receipt_id = $receipt->id;
                    $detailLog->detail_id = $detailData['detail_id'];
                    $detailLog->inspection_by = $this->uid;
                    $detailLog->inspection_time = time();
                    $detailLog->old_status = $oldDetailStatus;
                    $detailLog->new_status = intval($detailData['quality_status']);
                    $detailLog->notes = $detailData['abnormal_reason'] ?? '';
                    $detailLog->save();
                }
            }
            
            Db::commit();
            return json(['code' => 0, 'msg' => '质检完成']);
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 1, 'msg' => '质检失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 显示质检页面
     */
    public function inspection()
    {
        $id = input('id', 0, 'intval');
        if ($id <= 0) {
            return to_assign(1, '参数错误');
        }
        
        // 获取收货单信息
        $receipt = ReceiptModel::with(['supplier', 'warehouse', 'details.product'])->find($id);
        if (!$receipt) {
            return to_assign(1, '收货单不存在');
        }
        
        // 检查是否可以质检
        if (!$receipt->canInspect()) {
            return to_assign(1, '当前状态不能进行质检');
        }
        
        View::assign('receipt', $receipt);
        return View::fetch();
    }

    /**
     * 获取质检记录
     */
    public function getInspectionLogs()
    {
        if (!request()->isAjax()) {
            $this->error('非法请求');
        }
        
        $param = get_params();
        
        // 验证参数
        if (empty($param['receipt_id'])) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        
        try {
            $where = [
                'receipt_id' => $param['receipt_id']
            ];
            
            // 如果指定了明细ID，则查询该明细的质检记录
            if (!empty($param['detail_id'])) {
                $where['detail_id'] = $param['detail_id'];
            } else {
                // 查询整单的质检记录（detail_id为空）
                $where['detail_id'] = null;
            }
            
            // 获取质检记录
            $logs = ReceiptInspectionLogModel::where($where)
                ->with(['inspector'])
                ->order('inspection_time desc')
                ->select();
                
            $data = [];
            foreach ($logs as $log) {
                $statusChangeText = '';
                switch ($log->old_status) {
                    case 0:
                        $oldStatusText = '未检验';
                        break;
                    case 1:
                        $oldStatusText = '检验中';
                        break;
                    case 2:
                        $oldStatusText = '检验合格';
                        break;
                    case 3:
                        $oldStatusText = '检验不合格';
                        break;
                    default:
                        $oldStatusText = '未知';
                }
                
                switch ($log->new_status) {
                    case 0:
                        $newStatusText = '未检验';
                        break;
                    case 1:
                        $newStatusText = '检验中';
                        break;
                    case 2:
                        $newStatusText = '检验合格';
                        break;
                    case 3:
                        $newStatusText = '检验不合格';
                        break;
                    default:
                        $newStatusText = '未知';
                }
                
                $statusChangeText = "{$oldStatusText} → {$newStatusText}";
                
                $data[] = [
                    'inspection_time' => date('Y-m-d H:i:s', $log->inspection_time),
                    'inspector_name' => $log->inspector ? $log->inspector->name : '未知',
                    'status_change_text' => $statusChangeText,
                    'notes' => $log->notes,
                    'image_urls' => $log->image_urls
                ];
            }
            
            return json(['code' => 0, 'msg' => 'success', 'data' => $data]);
        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => $e->getMessage()]);
        }
    }
} 