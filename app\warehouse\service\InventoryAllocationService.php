<?php
declare (strict_types = 1);

namespace app\warehouse\service;

use think\Db;
use think\Exception;
use app\warehouse\model\InventoryLock;
use app\warehouse\model\InventoryRealtime;
use app\warehouse\service\InventoryLockServiceNew;
use app\warehouse\service\InventoryRealtimeService;

/**
 * 库存分配与锁定统一服务25.8.5
 * 
 * 功能说明：
 * 1. 统一的库存分配与锁定接口
 * 2. 入库时的自动分配功能
 * 3. 反审时的锁定释放功能
 * 4. 基于优先级的智能分配算法
 */
class InventoryAllocationService
{
    /**
     * 统一的库存分配与锁定接口
     * 
     * @param array $request 分配请求参数
     * @return array 分配结果
     * @throws Exception
     */
    public function allocateAndLock($request)
    {
        $productId = $request['product_id'];
        $warehouseId = $request['warehouse_id'];
        $requestQty = $request['quantity'];
        $refType = $request['ref_type'];
        $refId = $request['ref_id'];
        $refNo = $request['ref_no'] ?? '';
        $priority = $request['priority'] ?? $this->getDefaultPriority($refType);
        $notes = $request['notes'] ?? '';
        $createdBy = $request['created_by'] ?? 0;
        
        // 参数验证
        if ($requestQty <= 0) {
            throw new Exception('请求数量必须大于0');
        }
        
        // 1. 检查可用库存
        $availableQty = $this->getAvailableStock($productId, $warehouseId);
        
        if ($availableQty >= $requestQty) {
            // 2. 库存充足，直接分配并锁定
            return $this->directAllocateAndLock($request);
        } else if ($availableQty > 0) {
            // 3. 库存不足但有部分库存，部分分配
            return $this->partialAllocateAndLock($request, $availableQty);
        } else {
            // 4. 无库存，创建分配需求等待
            return $this->createAllocationRequest($request);
        }
    }
    
    /**
     * 入库触发的自动分配与锁定
     * 
     * @param int $productId 产品ID
     * @param int $warehouseId 仓库ID
     * @param float $inboundQty 入库数量
     * @return array 分配结果
     */
    public function autoAllocateOnInbound($productId, $warehouseId, $inboundQty)
    {
        // 1. 获取所有待分配的需求（按优先级排序）
        $pendingRequests = $this->getPendingAllocationRequests($productId, $warehouseId);
        
        if (empty($pendingRequests)) {
            return [
                'code' => 0,
                'msg' => '无待分配需求',
                'total_allocated' => 0,
                'allocation_details' => []
            ];
        }
        
        $remainingQty = $inboundQty;
        $allocationResults = [];
        
        Db::startTrans();
        try {
            // 2. 按优先级逐个分配并锁定
            foreach ($pendingRequests as $request) {
                if ($remainingQty <= 0) break;
                
                $pendingQty = $request['quantity'] - $request['allocated_quantity'];
                $allocateQty = min($remainingQty, $pendingQty);
                
                // 执行分配
                $result = $this->executeAllocation($request, $allocateQty);
                $allocationResults[] = $result;
                
                $remainingQty -= $allocateQty;
            }
            
            Db::commit();
            
            return [
                'code' => 0,
                'msg' => '自动分配完成',
                'total_allocated' => $inboundQty - $remainingQty,
                'remaining_stock' => $remainingQty,
                'allocation_details' => $allocationResults
            ];
            
        } catch (Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 反审释放锁定库存
     * 
     * @param array $params 反审参数
     * @return array 释放结果
     */
    public function releaseOnReverseAudit($params)
    {
        $refType = $params['ref_type'];
        $refId = $params['ref_id'];
        $operatorId = $params['operator_id'];
        $reason = $params['reason'] ?? '反审释放';
        
        // 1. 检查是否允许反审
        $this->validateReverseAudit($refType, $refId);
        
        // 2. 获取所有相关的锁定记录
        $lockRecords = $this->getActiveLockRecords($refType, $refId);
        
        if (empty($lockRecords)) {
            return ['code' => 0, 'msg' => '无需释放锁定库存'];
        }
        
        // 3. 检查每个锁定记录的可释放性
        $releaseResults = [];
        $hasError = false;
        $totalReleasedQty = 0;
        
        Db::startTrans();
        try {
            foreach ($lockRecords as $lock) {
                $releaseResult = $this->processLockRelease($lock, $reason, $operatorId);
                $releaseResults[] = $releaseResult;
                
                if ($releaseResult['code'] !== 0) {
                    $hasError = true;
                } else {
                    $totalReleasedQty += $lock['quantity'];
                }
            }
            
            if ($hasError) {
                Db::rollback();
                return ['code' => 1, 'msg' => '部分锁定释放失败', 'details' => $releaseResults];
            }
            
            // 4. 记录反审日志
            $this->logReverseAudit($refType, $refId, $lockRecords, $reason, $operatorId);
            
            // 5. 删除对应的分配需求记录
            $this->cancelAllocationRequests($refType, $refId);
            
            // 6. 释放成功后，重新触发分配（给其他等待的需求）
            $this->redistributeReleasedStock($lockRecords);
            
            Db::commit();
            
            return [
                'code' => 0, 
                'msg' => '锁定释放成功', 
                'total_released' => $totalReleasedQty,
                'details' => $releaseResults
            ];
            
        } catch (Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 获取可用库存数量
     */
    private function getAvailableStock($productId, $warehouseId)
    {
        $inventory = Db::name('inventory_realtime')
            ->where('product_id', $productId)
            ->where('warehouse_id', $warehouseId)
            ->field('available_quantity')
            ->find();

        return $inventory ? floatval($inventory['available_quantity']) : 0;
    }
    
    /**
     * 获取业务类型的默认优先级
     */
    private function getDefaultPriority($refType)
    {
        $priorityMap = [
            'production_order' => 90,      // 生产订单
            'customer_order' => 60,        // 客户订单
            'purchase_order' => 50,        // 采购订单
            'transfer' => 50,              // 调拨单
            'quality_check' => 40,         // 质检
        ];
        
        return $priorityMap[$refType] ?? 50;
    }
    
    /**
     * 直接分配并锁定（库存充足的情况）
     */
    private function directAllocateAndLock($request)
    {
        $lockService = new InventoryLockServiceNew();
        
        try {
            $lockResult = $lockService->lockInventory([
                'product_id' => $request['product_id'],
                'warehouse_id' => $request['warehouse_id'],
                'quantity' => $request['quantity'],
                'ref_type' => $request['ref_type'],
                'ref_id' => $request['ref_id'],
                'ref_no' => $request['ref_no'],
                'notes' => $request['notes'],
                'created_by' => $request['created_by'] ?? 0
            ]);
            
            return [
                'code' => 0,
                'status' => 'success',
                'msg' => '库存分配并锁定成功',
                'allocated_quantity' => $request['quantity'],
                'lock_id' => $lockResult['lock_record']['id']
            ];
            
        } catch (Exception $e) {
            throw new Exception('库存锁定失败：' . $e->getMessage());
        }
    }
    
    /**
     * 部分分配并锁定（库存不足但有部分库存）
     */
    private function partialAllocateAndLock($request, $availableQty)
    {
        Db::startTrans();
        try {
            // 1. 锁定可用库存
            $lockResult = $this->directAllocateAndLock(array_merge($request, [
                'quantity' => $availableQty
            ]));
            
            // 2. 创建剩余数量的分配需求
            $remainingQty = $request['quantity'] - $availableQty;
            $allocationRequest = $this->createAllocationRequest(array_merge($request, [
                'quantity' => $remainingQty
            ]));
            
            Db::commit();
            
            return [
                'code' => 0,
                'status' => 'partial',
                'msg' => '部分库存分配成功，剩余数量等待分配',
                'allocated_quantity' => $availableQty,
                'pending_quantity' => $remainingQty,
                'lock_id' => $lockResult['lock_id'],
                'request_id' => $allocationRequest['request_id']
            ];
            
        } catch (Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 创建分配需求记录（无库存的情况）
     */
    private function createAllocationRequest($request)
    {
        $data = [
            'product_id' => $request['product_id'],
            'warehouse_id' => $request['warehouse_id'],
            'quantity' => $request['quantity'],
            'allocated_quantity' => 0,
            'ref_type' => $request['ref_type'],
            'ref_id' => $request['ref_id'],
            'ref_no' => $request['ref_no'],
            'priority' => $request['priority'] ?? $this->getDefaultPriority($request['ref_type']),
            'status' => 1, // 待分配
            'request_time' => time(),
            'notes' => $request['notes'],
            'created_by' => $request['created_by'] ?? 0,
            'create_time' => time(),
            'update_time' => time()
        ];

        $requestId = Db::name('inventory_allocation_request')->insertGetId($data);

        return [
            'code' => 0,
            'status' => 'pending',
            'msg' => '库存不足，已创建分配需求等待库存到货',
            'allocated_quantity' => 0,
            'pending_quantity' => $request['quantity'],
            'request_id' => $requestId
        ];
    }

    /**
     * 获取待分配的需求列表（按优先级排序）
     */
    private function getPendingAllocationRequests($productId, $warehouseId)
    {
        return Db::name('inventory_allocation_request')
            ->where('product_id', $productId)
            ->where('warehouse_id', $warehouseId)
            ->where('status', 'in', [1, 2]) // 待分配和部分分配
            ->where('quantity', '>', Db::raw('allocated_quantity'))
            ->order('priority DESC, request_time ASC')
            ->select()
            ->toArray();
    }

    /**
     * 执行具体的分配操作
     */
    private function executeAllocation($request, $allocateQty)
    {
        try {
            // 1. 锁定库存
            $lockService = new InventoryLockServiceNew();
            $lockResult = $lockService->lockInventory([
                'product_id' => $request['product_id'],
                'warehouse_id' => $request['warehouse_id'],
                'quantity' => $allocateQty,
                'ref_type' => $request['ref_type'],
                'ref_id' => $request['ref_id'],
                'ref_no' => $request['ref_no'],
                'notes' => '自动分配锁定：' . $request['notes'],
                'created_by' => $request['created_by']
            ]);

            // 2. 更新分配需求记录
            $newAllocatedQty = $request['allocated_quantity'] + $allocateQty;
            $newStatus = ($newAllocatedQty >= $request['quantity']) ? 3 : 2; // 完全分配或部分分配

            Db::name('inventory_allocation_request')
                ->where('id', $request['id'])
                ->update([
                    'allocated_quantity' => $newAllocatedQty,
                    'status' => $newStatus,
                    'update_time' => time()
                ]);

            // 3. 记录分配历史
            Db::name('inventory_allocation_history')->insert([
                'request_id' => $request['id'],
                'product_id' => $request['product_id'],
                'warehouse_id' => $request['warehouse_id'],
                'allocated_quantity' => $allocateQty,
                'lock_id' => $lockResult['lock_record']['id'],
                'allocation_type' => 'auto',
                'allocation_source' => 'inbound',
                'notes' => '入库自动分配',
                'created_by' => $request['created_by'],
                'create_time' => time()
            ]);

            return [
                'code' => 0,
                'request_id' => $request['id'],
                'ref_type' => $request['ref_type'],
                'ref_id' => $request['ref_id'],
                'ref_no' => $request['ref_no'],
                'allocated_quantity' => $allocateQty,
                'total_allocated' => $newAllocatedQty,
                'lock_id' => $lockResult['lock_record']['id'],
                'status' => $newStatus == 3 ? 'completed' : 'partial'
            ];

        } catch (Exception $e) {
            return [
                'code' => 1,
                'request_id' => $request['id'],
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 验证是否允许反审
     */
    private function validateReverseAudit($refType, $refId)
    {
        switch ($refType) {
            case 'customer_order':
                return $this->validateSalesOrderReverseAudit($refId);
            case 'production_order':
                return $this->validateProductionOrderReverseAudit($refId);
            case 'purchase_order':
                return $this->validatePurchaseOrderReverseAudit($refId);
            default:
                throw new Exception('不支持的业务类型反审：' . $refType);
        }
    }

    /**
     * 销售订单反审验证
     */
    private function validateSalesOrderReverseAudit($orderId)
    {
        $order = Db::name('order')->find($orderId);
        if (!$order) {
            throw new Exception('订单不存在');
        }

        // 检查发货状态
        $deliveryCount = Db::name('delivery_detail')
            ->alias('d')
            ->join('delivery o', 'd.delivery_id = o.id')
            ->where('o.order_id', $orderId)
            ->where('o.status', '>', 0) // 已确认的发货单
            ->count();

        if ($deliveryCount > 0) {
            throw new Exception('订单已有发货记录，不允许反审');
        }

        return true;
    }

    /**
     * 生产订单反审验证
     */
    private function validateProductionOrderReverseAudit($orderId)
    {
        $order = Db::name('production_order')->find($orderId);
        if (!$order) {
            throw new Exception('生产订单不存在');
        }

        // 检查领料状态
        $materialIssueCount = Db::name('material_issue_detail')
            ->alias('d')
            ->join('material_issue o', 'd.issue_id = o.id')
            ->where('o.production_order_id', $orderId)
            ->where('o.status', '>', 0)
            ->count();

        if ($materialIssueCount > 0) {
            throw new Exception('生产订单已有领料记录，不允许反审');
        }

        return true;
    }

    /**
     * 采购订单反审验证
     */
    private function validatePurchaseOrderReverseAudit($orderId)
    {
        $order = Db::name('purchase_order')->find($orderId);
        if (!$order) {
            throw new Exception('采购订单不存在');
        }

        // 检查入库状态
        $inboundCount = Db::name('inbound_detail')
            ->alias('d')
            ->join('inbound o', 'd.inbound_id = o.id')
            ->where('o.purchase_order_id', $orderId)
            ->where('o.status', '>', 0)
            ->count();

        if ($inboundCount > 0) {
            throw new Exception('采购订单已有入库记录，不允许反审');
        }

        return true;
    }

    /**
     * 获取活跃的锁定记录
     */
    private function getActiveLockRecords($refType, $refId)
    {
        return Db::name('inventory_lock')
            ->where('ref_type', $refType)
            ->where('ref_id', $refId)
            ->where('status', InventoryLock::STATUS_LOCKED)
            ->select()
            ->toArray();
    }

    /**
     * 处理单个锁定记录的释放
     */
    private function processLockRelease($lock, $reason, $operatorId)
    {
        try {
            // 检查锁定状态
            if ($lock['status'] != InventoryLock::STATUS_LOCKED) {
                return [
                    'code' => 1,
                    'msg' => "锁定记录状态异常：{$lock['id']}"
                ];
            }

            // 释放锁定
            $lockService = new InventoryLockServiceNew();
            $result = $lockService->releaseLock($lock['id'], $operatorId);

            return [
                'code' => 0,
                'msg' => '释放成功',
                'lock_id' => $lock['id'],
                'product_id' => $lock['product_id'],
                'quantity' => $lock['quantity']
            ];

        } catch (Exception $e) {
            return [
                'code' => 1,
                'msg' => "释放失败：" . $e->getMessage(),
                'lock_id' => $lock['id']
            ];
        }
    }

    /**
     * 记录反审日志
     */
    private function logReverseAudit($refType, $refId, $lockRecords, $reason, $operatorId)
    {
        $refNo = '';
        $totalReleased = 0;

        // 获取业务单号
        switch ($refType) {
            case 'customer_order':
                $order = Db::name('order')->find($refId);
                $refNo = $order['order_no'] ?? '';
                break;
            case 'production_order':
                $order = Db::name('production_order')->find($refId);
                $refNo = $order['order_no'] ?? '';
                break;
            case 'purchase_order':
                $order = Db::name('purchase_order')->find($refId);
                $refNo = $order['order_no'] ?? '';
                break;
        }

        // 计算释放总量
        foreach ($lockRecords as $lock) {
            $totalReleased += $lock['quantity'];
        }

        // 获取操作人姓名
        $operator = Db::name('admin')->find($operatorId);
        $operatorName = $operator['name'] ?? '';

        Db::name('reverse_audit_log')->insert([
            'ref_type' => $refType,
            'ref_id' => $refId,
            'ref_no' => $refNo,
            'released_locks' => json_encode($lockRecords),
            'released_quantity' => $totalReleased,
            'reason' => $reason,
            'operator_id' => $operatorId,
            'operator_name' => $operatorName,
            'create_time' => time()
        ]);
    }

    /**
     * 取消分配需求记录
     */
    private function cancelAllocationRequests($refType, $refId)
    {
        Db::name('inventory_allocation_request')
            ->where('ref_type', $refType)
            ->where('ref_id', $refId)
            ->where('status', 'in', [1, 2]) // 待分配和部分分配
            ->update([
                'status' => 4, // 已取消
                'update_time' => time()
            ]);
    }

    /**
     * 重新分配释放的库存
     */
    private function redistributeReleasedStock($releasedLocks)
    {
        // 按产品分组统计释放的库存
        $releasedStock = [];
        foreach ($releasedLocks as $lock) {
            $key = $lock['product_id'] . '_' . $lock['warehouse_id'];
            if (!isset($releasedStock[$key])) {
                $releasedStock[$key] = [
                    'product_id' => $lock['product_id'],
                    'warehouse_id' => $lock['warehouse_id'],
                    'quantity' => 0
                ];
            }
            $releasedStock[$key]['quantity'] += $lock['quantity'];
        }

        // 对每个产品重新触发分配
        foreach ($releasedStock as $stock) {
            try {
                $this->autoAllocateOnInbound(
                    $stock['product_id'],
                    $stock['warehouse_id'],
                    $stock['quantity']
                );
            } catch (Exception $e) {
                // 记录错误日志，但不影响整体流程
                error_log("重新分配库存失败：" . $e->getMessage());
            }
        }
    }

    /**
     * 获取产品的库存分配统计
     */
    public function getAllocationSummary($productId, $warehouseId = null)
    {
        $where = ['product_id' => $productId];
        if ($warehouseId) {
            $where['warehouse_id'] = $warehouseId;
        }

        $summary = Db::name('inventory_allocation_request')
            ->where($where)
            ->where('status', 'in', [1, 2, 3]) // 排除已取消的
            ->field([
                'ref_type',
                'COUNT(*) as request_count',
                'SUM(quantity) as total_quantity',
                'SUM(allocated_quantity) as allocated_quantity',
                'SUM(quantity - allocated_quantity) as pending_quantity'
            ])
            ->group('ref_type')
            ->select()
            ->toArray();

        return $summary;
    }

    /**
     * 批量分配库存
     */
    public function batchAllocateAndLock($requests)
    {
        $results = [];
        $hasError = false;

        Db::startTrans();
        try {
            foreach ($requests as $request) {
                $result = $this->allocateAndLock($request);
                $results[] = $result;

                if ($result['code'] !== 0) {
                    $hasError = true;
                }
            }

            if ($hasError) {
                Db::rollback();
                return ['code' => 1, 'msg' => '批量分配部分失败', 'details' => $results];
            }

            Db::commit();
            return ['code' => 0, 'msg' => '批量分配成功', 'details' => $results];

        } catch (Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
}
