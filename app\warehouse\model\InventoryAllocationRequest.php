<?php
declare (strict_types = 1);

namespace app\warehouse\model;

use think\Model;

/**
 * 库存分配需求模型
 */
class InventoryAllocationRequest extends Model
{
    // 设置表名
    protected $name = 'inventory_allocation_request';
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    
    // 状态常量
    const STATUS_PENDING = 1;       // 待分配
    const STATUS_PARTIAL = 2;       // 部分分配
    const STATUS_COMPLETED = 3;     // 完全分配
    const STATUS_CANCELLED = 4;     // 已取消
    
    // 业务类型常量
    const REF_TYPE_CUSTOMER_ORDER = 'customer_order';      // 客户订单
    const REF_TYPE_PRODUCTION_ORDER = 'production_order';  // 生产订单
    const REF_TYPE_PURCHASE_ORDER = 'purchase_order';      // 采购订单
    const REF_TYPE_TRANSFER = 'transfer';                  // 调拨单
    const REF_TYPE_QUALITY_CHECK = 'quality_check';       // 质检
    
    // 设置字段信息
    protected $schema = [
        'id'                 => 'int',
        'product_id'         => 'int',
        'warehouse_id'       => 'int',
        'quantity'           => 'float',
        'allocated_quantity' => 'float',
        'ref_type'           => 'string',
        'ref_id'             => 'int',
        'ref_no'             => 'string',
        'priority'           => 'int',
        'status'             => 'int',
        'request_time'       => 'int',
        'notes'              => 'string',
        'created_by'         => 'int',
        'create_time'        => 'int',
        'update_time'        => 'int',
    ];
    
    /**
     * 关联产品信息
     */
    public function product()
    {
        return $this->belongsTo('think\Model', 'product_id')->setTable('oa_product');
    }

    /**
     * 关联仓库信息
     */
    public function warehouse()
    {
        return $this->belongsTo('think\Model', 'warehouse_id')->setTable('oa_warehouse');
    }
    
    /**
     * 关联创建人信息
     */
    public function creator()
    {
        return $this->belongsTo('app\admin\model\Admin', 'created_by');
    }
    
    /**
     * 关联分配历史记录
     */
    public function allocationHistory()
    {
        return $this->hasMany('app\warehouse\model\InventoryAllocationHistory', 'request_id');
    }
    
    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $statusMap = [
            self::STATUS_PENDING => '待分配',
            self::STATUS_PARTIAL => '部分分配',
            self::STATUS_COMPLETED => '完全分配',
            self::STATUS_CANCELLED => '已取消'
        ];
        
        return $statusMap[$data['status']] ?? '未知状态';
    }
    
    /**
     * 获取业务类型文本
     */
    public function getRefTypeTextAttr($value, $data)
    {
        $typeMap = [
            self::REF_TYPE_CUSTOMER_ORDER => '客户订单',
            self::REF_TYPE_PRODUCTION_ORDER => '生产订单',
            self::REF_TYPE_PURCHASE_ORDER => '采购订单',
            self::REF_TYPE_TRANSFER => '调拨单',
            self::REF_TYPE_QUALITY_CHECK => '质检'
        ];
        
        return $typeMap[$data['ref_type']] ?? '未知类型';
    }
    
    /**
     * 获取待分配数量
     */
    public function getPendingQuantityAttr($value, $data)
    {
        return $data['quantity'] - $data['allocated_quantity'];
    }
    
    /**
     * 获取分配进度百分比
     */
    public function getProgressAttr($value, $data)
    {
        if ($data['quantity'] <= 0) {
            return 0;
        }
        
        return round(($data['allocated_quantity'] / $data['quantity']) * 100, 2);
    }
    
    /**
     * 获取等待天数
     */
    public function getWaitDaysAttr($value, $data)
    {
        return ceil((time() - $data['request_time']) / 86400);
    }
    
    /**
     * 创建分配需求
     */
    public static function createRequest($data)
    {
        $request = new self();
        $request->save([
            'product_id' => $data['product_id'],
            'warehouse_id' => $data['warehouse_id'],
            'quantity' => $data['quantity'],
            'allocated_quantity' => $data['allocated_quantity'] ?? 0,
            'ref_type' => $data['ref_type'],
            'ref_id' => $data['ref_id'],
            'ref_no' => $data['ref_no'] ?? '',
            'priority' => $data['priority'] ?? 50,
            'status' => $data['status'] ?? self::STATUS_PENDING,
            'request_time' => $data['request_time'] ?? time(),
            'notes' => $data['notes'] ?? '',
            'created_by' => $data['created_by'] ?? 0
        ]);
        
        return $request;
    }
    
    /**
     * 更新分配数量
     */
    public function updateAllocation($allocatedQty, $notes = '')
    {
        $newAllocatedQty = $this->allocated_quantity + $allocatedQty;
        $newStatus = ($newAllocatedQty >= $this->quantity) ? self::STATUS_COMPLETED : self::STATUS_PARTIAL;
        
        $this->save([
            'allocated_quantity' => $newAllocatedQty,
            'status' => $newStatus,
            'notes' => $notes ? $this->notes . '; ' . $notes : $this->notes
        ]);
        
        return $this;
    }
    
    /**
     * 取消分配需求
     */
    public function cancel($reason = '')
    {
        $this->save([
            'status' => self::STATUS_CANCELLED,
            'notes' => $this->notes . '; 取消原因: ' . $reason
        ]);
        
        return $this;
    }
    
    /**
     * 获取指定产品的待分配需求
     */
    public static function getPendingRequests($productId, $warehouseId = null)
    {
        $where = [
            'product_id' => $productId,
            'status' => ['in', [self::STATUS_PENDING, self::STATUS_PARTIAL]]
        ];
        
        if ($warehouseId) {
            $where['warehouse_id'] = $warehouseId;
        }
        
        return self::where($where)
            ->where('quantity', '>', 'allocated_quantity')
            ->order('priority DESC, request_time ASC')
            ->select();
    }
    
    /**
     * 获取业务相关的分配需求
     */
    public static function getByBusiness($refType, $refId)
    {
        return self::where('ref_type', $refType)
            ->where('ref_id', $refId)
            ->select();
    }
    
    /**
     * 获取分配统计
     */
    public static function getAllocationStats($productId = null, $warehouseId = null, $refType = null)
    {
        $where = [];
        
        if ($productId) {
            $where['product_id'] = $productId;
        }
        
        if ($warehouseId) {
            $where['warehouse_id'] = $warehouseId;
        }
        
        if ($refType) {
            $where['ref_type'] = $refType;
        }
        
        return self::where($where)
            ->field([
                'status',
                'COUNT(*) as count',
                'SUM(quantity) as total_quantity',
                'SUM(allocated_quantity) as allocated_quantity',
                'SUM(quantity - allocated_quantity) as pending_quantity'
            ])
            ->group('status')
            ->select()
            ->toArray();
    }
    
    /**
     * 清理过期的分配需求
     */
    public static function cleanupExpired($maxWaitDays = 30)
    {
        $expireTime = time() - ($maxWaitDays * 86400);
        
        return self::where('status', 'in', [self::STATUS_PENDING, self::STATUS_PARTIAL])
            ->where('request_time', '<', $expireTime)
            ->update([
                'status' => self::STATUS_CANCELLED,
                'notes' => '系统自动取消：超过最大等待时间',
                'update_time' => time()
            ]);
    }
    
    /**
     * 搜索器：按状态搜索
     */
    public function searchStatusAttr($query, $value)
    {
        if ($value !== '') {
            $query->where('status', $value);
        }
    }
    
    /**
     * 搜索器：按业务类型搜索
     */
    public function searchRefTypeAttr($query, $value)
    {
        if ($value !== '') {
            $query->where('ref_type', $value);
        }
    }
    
    /**
     * 搜索器：按产品搜索
     */
    public function searchProductIdAttr($query, $value)
    {
        if ($value !== '') {
            $query->where('product_id', $value);
        }
    }
    
    /**
     * 搜索器：按仓库搜索
     */
    public function searchWarehouseIdAttr($query, $value)
    {
        if ($value !== '') {
            $query->where('warehouse_id', $value);
        }
    }
    
    /**
     * 搜索器：按时间范围搜索
     */
    public function searchCreateTimeAttr($query, $value)
    {
        if (is_array($value) && count($value) == 2) {
            $query->whereBetweenTime('create_time', $value[0], $value[1]);
        }
    }
}
