<?php


declare (strict_types = 1);
namespace app\api\controller;

use app\api\BaseController;
use app\api\model\EditLog;
use think\facade\Db;
use app\Produce\model\ProductionFeeding;
use think\facade\Session;

class Test extends BaseController
{
	public function index()
	{

		// 获取订单详情
		$action_id = 22;
		$orderDetails = Db::name('customer_order_detail')->where(['order_id' => $action_id])->select();

        
		foreach ($orderDetails as $orderDetail) {
			$productId = $orderDetail['product_id'];
			$quantity = $orderDetail['quantity'];
			$orderDetail_id=$orderDetail['id'];
		
			// 检查产品是否有BOM表
			$bomExists = Db::name('bom_master')->where(['product_id' => $productId, 'check_status' => 2])->find();
           
			if ($bomExists) {
				// 产品有BOM表，查询所有BOM物料
				$bomDetails = Db::name('bom_item')->where(['bom_id' => $bomExists['id']])->select();
				
				foreach ($bomDetails as $bomDetail) {
					$materialId = $bomDetail['material_id'];
					$requiredQuantity = $bomDetail['qty'] * $quantity; // BOM用量 * 销售数量
					
					// 获取当前库存
					$inventory = Db::name('inventory')->where(['product_id' => $materialId])->find();
					$currentStock = $inventory ? $inventory['quantity'] : 0;
                    
					// 获取在途数量
					$inTransit = Db::name('purchase_order_detail')
						->alias('pod')
						->join('purchase_order po', 'po.id = pod.order_id')
						->where([
							'pod.product_id' => $materialId,
							['po.status', 'in', [1, 2]]
						])
						->sum('pod.quantity');
                    
                       
					// 计算可用库存
					$availableStock = $currentStock + $inTransit;
					
					// 如果库存不足，插入需求数据
					if ($availableStock < $requiredQuantity) {
						$needQuantity = $requiredQuantity - $availableStock;
						// 向物料需求表插入数据
						Db::name('material_requirement')->insert([
							'order_id' => $action_id,
							'product_id' => $materialId,
							'quantity' => $needQuantity,
							'source_id' => $orderDetail_id,
							'source_type' => 'customer_order',
							'status' => 0, // 未处理
							'gap'=>0,
							'create_time' => time(),
							'create_by' => $this->uid
						]);
					}
				}
			} else {
				// 产品没有BOM表，直接检查产品库存
				$inventory = Db::name('inventory')->where(['product_id' => $productId])->find();
				$currentStock = $inventory ? $inventory['quantity'] : 0;
				
				// 获取在途数量
				$inTransit = Db::name('purchase_order_detail')
					->alias('pod')
					->join('purchase_order po', 'po.id = pod.order_id')
					->where([
						'pod.product_id' => $productId,
						['po.status', 'in', [1, 2]] // 采购中或已审核状态
					])
					->sum('pod.quantity');
				
				// 计算可用库存
				$availableStock = $currentStock + $inTransit;
				
				// 如果库存不足，插入需求数据
				if ($availableStock < $quantity) {
					$needQuantity = $quantity - $availableStock;
					// 向物料需求表插入数据
					Db::name('material_requirement')->insert([
						'order_id' => $action_id,
						'product_id' => $productId,
						'quantity' => $needQuantity,
						'source_id' => $orderDetail_id,
						'source_type' => 'customer_order',
						'status' => 0, // 未处理
						'gap'=>0,
						'create_time' => time(),
						'create_by' => $this->uid
					]);
				}
			}
		}
		//end


		return 'hello';
	}
	//销售订单及物料需求表
	public function getmrppurchase_suggestion()
	{
		// 获取分页参数
		$page = intval(request()->param('page', 1));
		$limit = intval(request()->param('limit', 15));

		// 获取搜索参数
		$business_no = request()->param('business_no', '');
		$product_name = request()->param('product_name', '');
		$salesman_name = request()->param('salesman_name', '');

		// 先获取已审核订单的ID
		 $checkedOrderIds = Db::name('customer_order')
		 	->where('check_status', 2)
			->column('id');

		trace('已审核订单ID列表：' . json_encode($checkedOrderIds), 'debug');

		// 获取这些订单对应的订单明细ID
		$checkedDetailIds = Db::name('customer_order_detail')
			->whereIn('order_id', $checkedOrderIds)
			->column('id');
		trace('已审核订单的明细ID列表：' . json_encode($checkedDetailIds), 'debug');

		// 获取物料需求列表
		$query = Db::name('material_requirement')
			->alias('mr')
			->join('product p', 'p.id = mr.product_id')
			->leftJoin('customer_order_detail cod', 'cod.id = mr.source_id AND mr.source_type = "customer_order_detail"')
			->leftJoin('customer_order co', 'co.id = cod.order_id')
			->leftJoin('admin au', 'au.id = co.create_user_id')  // 关联业务员信息
			// 关联主产品信息（当source_type为customer_order_detail时，通过订单明细获取主产品）
			->leftJoin('product main_p', 'main_p.id = cod.product_id')
			->whereIn('mr.source_id', $checkedDetailIds)  // 只查询已审核订单明细的需求
			->where(function($query) {
				$query->where('mr.gap', '<', Db::raw('mr.quantity'))
					->whereOr('mr.gap', 'is', null)
					->whereOr('mr.gap', '=', 0);
			});

		// 添加搜索条件
		if (!empty($business_no)) {
			$query->where('co.order_no', 'like', "%{$business_no}%");
		}
		if (!empty($product_name)) {
			$query->where('p.title', 'like', "%{$product_name}%");
		}
		if (!empty($salesman_name)) {
			$query->where('au.nickname', 'like', "%{$salesman_name}%");
		}

		// 选择需要的字段
		$query->field([
			'mr.*',
			'p.title as product_name',
			'p.material_code as product_code',
			'co.order_no as business_no',
			'mr.gap - mr.quantity as gap',
			'co.check_status',
			'mr.source_type',
			'au.nickname as salesman_name',  // 添加业务员名称字段
			'main_p.title as main_product_name',  // 主产品名称（用于BOM物料）
			'main_p.id as main_product_id'  // 主产品ID（用于判断是否为主产品）
		])
		->order('mr.create_time desc');

		// 获取总记录数
		$count = $query->count();
		
		// 使用page和limit方法进行分页
		$list = $query->page($page)
			->limit($limit)
			->select()
			->toArray();

		// 添加更详细的调试信息
		trace('物料需求查询SQL：' . Db::getLastSql(), 'debug');
		trace('查询结果数量：' . count($list), 'debug');
		trace('分页参数：page=' . $page . ', limit=' . $limit, 'debug');
		trace('总记录数：' . $count, 'debug');

		if (!empty($list)) {
			trace('第一条记录信息：' . json_encode($list[0], JSON_UNESCAPED_UNICODE), 'debug');
		}

		// 获取所有产品ID，用于批量查询库存和在途数据
		$productIds = array_unique(array_column($list, 'product_id'));

		// 批量获取实时库存数据（使用新的inventory_realtime表）
		$inventoryMap = [];
		if (!empty($productIds)) {
			$inventoryData = Db::name('inventory_realtime')
				->field('product_id, SUM(quantity) as total_quantity, SUM(available_quantity) as available_quantity, SUM(locked_quantity) as locked_quantity')
				->whereIn('product_id', $productIds)
				->group('product_id')
				->select()
				->toArray();

			foreach ($inventoryData as $inventory) {
				$inventoryMap[$inventory['product_id']] = [
					'total_quantity' => $inventory['total_quantity'],
					'available_quantity' => $inventory['available_quantity'],
					'locked_quantity' => $inventory['locked_quantity']
				];
			}
		}

		// 批量获取在途库存数据（采购订单中未完全入库的数量）
		$inTransitMap = [];
		if (!empty($productIds)) {
			$inTransitData = Db::name('purchase_order_detail')
				->alias('d')
				->join('purchase_order o', 'd.order_id = o.id')
				->whereIn('d.product_id', $productIds)
				->where('o.status', 'in', [2, 3, 4]) // 已审核，部分入库，未完成的订单
				->where('d.received_quantity', '<', Db::raw('d.quantity')) // 未完全入库
				->field('d.product_id, SUM(d.quantity - d.received_quantity) as in_transit_quantity')
				->group('d.product_id')
				->select()
				->toArray();

			foreach ($inTransitData as $inTransit) {
				$inTransitMap[$inTransit['product_id']] = $inTransit['in_transit_quantity'];
			}
		}

		// 批量获取在途库存的锁定数据（采购订单相关的锁定）
		$inTransitLockedMap = [];
		if (!empty($productIds)) {
			$inTransitLockedData = Db::name('inventory_lock')
				->whereIn('product_id', $productIds)
				->where('ref_type', 'purchase_order') // 采购订单相关的锁定
				->where('status', 1) // 锁定中
				->field('product_id, SUM(quantity) as locked_quantity')
				->group('product_id')
				->select()
				->toArray();

			foreach ($inTransitLockedData as $locked) {
				$inTransitLockedMap[$locked['product_id']] = $locked['locked_quantity'];
			}
		}

		foreach ($list as &$item) {
			$item['create_time'] = date('Y-m-d H:i:s', $item['create_time']);
			$item['status_text'] = $item['status'] == 0 ? '未处理' : '已处理';

			// 添加库存信息
			$productId = $item['product_id'];

			// 在库数量（总库存）
			$item['stock_quantity'] = isset($inventoryMap[$productId]) ? $inventoryMap[$productId]['total_quantity'] : 0;

			// 在库可用数量（已扣除锁定）
			$stockAvailable = isset($inventoryMap[$productId]) ? $inventoryMap[$productId]['available_quantity'] : 0;

			// 在途总数量
			$item['in_transit_quantity'] = isset($inTransitMap[$productId]) ? $inTransitMap[$productId] : 0;

			// 在途锁定数量
			$inTransitLocked = isset($inTransitLockedMap[$productId]) ? $inTransitLockedMap[$productId] : 0;

			// 在途可用数量（在途总数量 - 在途锁定数量）
			$inTransitAvailable = max(0, $item['in_transit_quantity'] - $inTransitLocked);

			// 添加详细的库存信息到返回数据
			$item['stock_available'] = $stockAvailable; // 在库可用
			$item['in_transit_available'] = $inTransitAvailable; // 在途可用
			$item['in_transit_locked'] = $inTransitLocked; // 在途锁定
			$item['stock_locked'] = isset($inventoryMap[$productId]) ? $inventoryMap[$productId]['locked_quantity'] : 0; // 在库锁定

			// 重新计算剩余缺口（需求数量 - 在库可用 - 在途可用）
			$totalAvailable = $stockAvailable + $inTransitAvailable;
			$item['remaining_gap'] = max(0, $item['quantity'] - $totalAvailable);

			// 设置主/BOM标识
			if ($item['source_type'] == 'customer_order_detail') {
				// 检查当前物料是否就是订单明细中的主产品
				if ($item['product_id'] == $item['main_product_id']) {
					// 主产品：显示"主"
					$item['main_or_bom'] = '主';
					$item['item_type'] = '主产品';
				} else {
					// BOM物料：显示所属主产品名称  main_product_id
					$item['main_or_bom'] = $item['main_product_name'] ?: '未知主产品';
					$item['item_type'] = 'BOM物料';
				}
			} else {
				// 其他类型：显示"主"
				$item['main_or_bom'] = '主';
				$item['item_type'] = '主产品';
			}

			// 根据不同的source_type获取对应的业务单号
			if ($item['source_type'] && $item['source_id']) {
				switch ($item['source_type']) {
					case 'customer_order_detail':
						// BOM物料需求
						$item['types'] = 'BOM物料';
						if (empty($item['business_no'])) {
							$item['business_no'] = '未知';
						}
						break;
					case 'customer_order':
						// 主产品需求
						$item['types'] = '主产品';
						if (empty($item['business_no'])) {
							$item['business_no'] = '未知';
						}
						break;
					// 可以根据需要添加其他业务类型的处理
				}
			}
		}
		unset($item);

		// 包含分页信息的完整返回
		return json([
			'code' => 0,
			'msg' => 'success',
			'count' => $count,
			'data' => $list
		]);

	}


	  /**
     * 获取BOM详细信息
     */
    public function getBomInfo()
    {
        $product_id = request()->param('id/d', 0);
        
        if ($product_id <= 0) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        
        // 查询最新有效的BOM信息
        $bomInfo = Db::name('bom_master')
            ->where('product_id', $product_id)
            ->where('check_status', 2) // 已审核状态
            ->where('delete_time', 0)
            ->order('create_time desc')
            ->find();

            
            
        if (!$bomInfo) {
            return json(['code' => 1, 'msg' => '该产品没有有效的BOM']);
        }
        
        // 获取产品信息
        $productInfo = Db::name('product')
            ->where('id', $product_id)
            ->field('id, title, material_code, specs, unit')
            ->find();
            
        // 获取BOM子项
        $bomItems = Db::name('bom_item')
            ->alias('bi')
            ->join('product p', 'p.id = bi.material_id', 'left')
            ->where('bi.bom_id', $bomInfo['id'])
            ->where('bi.is_needed', 1) // 只获取需要的子项
            ->field('bi.*, p.title as material_name, p.material_code, p.specs as product_specs, p.unit as uom_name')
            ->select()
            ->toArray();
            
        // 获取每个物料的库存
        foreach ($bomItems as &$item) {
            // 获取库存数量
            $item['stock'] = $this->getProductStock($item['material_id']);
        }
        
        $bomInfo['items'] = $bomItems;
        $bomInfo['product'] = $productInfo;
        
        return json(['code' => 0, 'msg' => '获取成功', 'data' => $bomInfo]);
    }

	
    /**
     * 获取产品当前库存
     * @param int $product_id 产品ID
     * @return float 库存数量
     */
    private function getProductStock($product_id)
    {
        // 获取可用库存
        $stock = Db::name('inventory')
            ->where('product_id', $product_id)
            ->where('status', 1) // 可用状态
            ->sum('quantity');
            
        return floatval($stock);
    }

}
