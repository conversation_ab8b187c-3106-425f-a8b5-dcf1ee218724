<?php
declare (strict_types = 1);

namespace app\warehouse\controller;

use app\base\BaseController;
use app\warehouse\model\InventoryRealtime;
use app\warehouse\service\InventoryRealtimeService;
use think\response\Json;
use think\facade\Request;
use think\facade\View;

/**
 * 实时库存管理控制器
 */
class InventoryRealtimeController extends BaseController
{
    /**
     * 模型对象
     * @var InventoryRealtime
     */
    protected $model = null;
    
    /**
     * 服务对象
     * @var InventoryRealtimeService
     */
    protected $service = null;
    
    public function initialize()
    {
        parent::initialize();
        $this->model = new InventoryRealtime();
        // 暂时注释掉服务类，先测试基本功能
        // $this->service = new \app\warehouse\service\InventoryRealtimeService();
    }
    
    /**
     * 库存列表
     */
    public function index()
    {
        if (Request::isAjax()) {
            // 搜索条件
            $filter = Request::get();

            // 检查是否显示所有产品（包括零库存）
            $showAll = $filter['show_all'] ?? false;

            if ($showAll) {
                // 显示所有产品，包括零库存
                $list = \think\facade\Db::name('product')
                    ->alias('p')
                    ->leftJoin('inventory_realtime ir', 'p.id = ir.product_id')
                    ->leftJoin('warehouse w', 'ir.warehouse_id = w.id')
                    ->field('
                        p.id as product_id,
                        p.title as product_title,
                        p.material_code as product_code,
                        p.specs as specs,
                        p.unit as product_unit,
                        IFNULL(ir.id, 0) as id,
                        IFNULL(ir.warehouse_id, 1) as warehouse_id,
                        IFNULL(w.name, "默认仓库") as warehouse_name,
                        IFNULL(ir.quantity, 0) as quantity,
                        IFNULL(ir.available_quantity, 0) as available_quantity,
                        IFNULL(ir.locked_quantity, 0) as locked_quantity,
                        IFNULL(ir.unit, p.unit) as unit,
                        IFNULL(ir.cost_price, 0) as cost_price,
                        IFNULL(ir.create_time, 0) as create_time,
                        IFNULL(ir.update_time, 0) as update_time
                    ')
                    ->where('p.delete_time', 0)
                    ->order('p.id desc')
                    ->paginate([
                        'list_rows' => $filter['limit'] ?? 10,
                        'query' => $filter
                    ]);
            } else {
                // 只显示有库存记录的产品
                $list = $this->model
                    ->with(['product', 'warehouse'])
                    ->withSearch(['product_id', 'warehouse_id', 'keywords'], $filter)
                    ->order('id desc')
                    ->paginate([
                        'list_rows' => $filter['limit'] ?? 10,
                        'query' => $filter
                    ]);
            }

            return json(['code' => 0, 'msg' => '', 'count' => $list->total(), 'data' => $list->items()]);
        }

        return View::fetch('inventory_realtime/index');
    }
    
    /**
     * 库存详情
     */
    public function detail()
    {
        $id = Request::param('id');
        $inventory = $this->model->with(['product', 'warehouse'])->find($id);
        
        if (!$inventory) {
            return json(['code' => 1, 'msg' => '库存记录不存在']);
        }
        
        View::assign('inventory', $inventory);
        return View::fetch('inventory_realtime/detail');
    }
    
    /**
     * 库存状态查询
     */
    public function status()
    {
        $productId = Request::param('product_id');
        $warehouseId = Request::param('warehouse_id', 0);
        
        if (!$productId) {
            return json(['code' => 1, 'msg' => '产品ID不能为空']);
        }
        
        try {
            // 暂时使用模型直接查询
            $inventory = $this->model->where([
                'product_id' => $productId,
                'warehouse_id' => $warehouseId
            ])->with(['product', 'warehouse'])->find();

            if (!$inventory) {
                $status = [
                    'product_id' => $productId,
                    'warehouse_id' => $warehouseId,
                    'quantity' => 0,
                    'available_quantity' => 0,
                    'locked_quantity' => 0
                ];
            } else {
                $status = $inventory->toArray();
            }

            return json(['code' => 0, 'msg' => '查询成功', 'data' => $status]);
        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => $e->getMessage()]);
        }
    }
    
    /**
     * 入库操作
     */
    public function inbound()
    {
        if (Request::isPost()) {
            $params = Request::post();
            
            // 验证必填字段
            $validate = [
                'product_id' => 'require|integer|gt:0',
                'warehouse_id' => 'require|integer|gt:0',
                'quantity' => 'require|float|gt:0',
                'unit' => 'require',
                'cost_price' => 'require|float|egt:0'
            ];
            
            $this->validate($params, $validate);
            
            try {
                $result = $this->model->increaseStock(
                    $params['product_id'],
                    $params['warehouse_id'],
                    $params['quantity'],
                    $params['unit'],
                    $params['cost_price'],
                    $params['ref_type'] ?? 'manual',
                    $params['ref_id'] ?? 0,
                    $params['ref_no'] ?? '',
                    $params['notes'] ?? ''
                );
                
                return json(['code' => 0, 'msg' => '入库成功', 'data' => $result]);
            } catch (\Exception $e) {
                return json(['code' => 1, 'msg' => $e->getMessage()]);
            }
        }

        // GET 请求，显示入库页面
        $data = [];

        // 获取URL参数
        $productId = Request::get('product_id', 0);
        $warehouseId = Request::get('warehouse_id', 0);

        // 如果有产品ID，获取产品信息
        if ($productId > 0) {
            $productModel = new \app\product\model\Product();
            $product = $productModel->find($productId);
            if ($product) {
                $data['product'] = $product->toArray();
            }
        }

        // 如果有仓库ID，获取仓库信息
        if ($warehouseId > 0) {
            $warehouseModel = new \app\warehouse\model\Warehouse();
            $warehouse = $warehouseModel->find($warehouseId);
            if ($warehouse) {
                $data['warehouse'] = $warehouse->toArray();
            }
        }

        // 获取所有产品列表（用于下拉选择）
        $productModel = new \app\product\model\Product();
        $products = $productModel->where('status', 1)->field('id,title,material_code,unit,specs')->select();
        $data['products'] = $products->toArray();

        // 获取所有仓库列表（用于下拉选择）
        $warehouseModel = new \app\warehouse\model\Warehouse();
        $warehouses = $warehouseModel->where('status', 1)->field('id,name,code')->select();
        $data['warehouses'] = $warehouses->toArray();

        return View::fetch('inventory_realtime/inbound', $data);
    }
    
    /**
     * 出库操作
     */
    public function outbound()
    {
        if (Request::isPost()) {
            $params = Request::post();
            
            // 验证必填字段
            $validate = [
                'product_id' => 'require|integer|gt:0',
                'warehouse_id' => 'require|integer|gt:0',
                'quantity' => 'require|float|gt:0'
            ];
            
            $this->validate($params, $validate);
            
            try {
                // 暂时简化出库操作
                return json(['code' => 1, 'msg' => '出库功能暂未实现']);
                
                return json(['code' => 0, 'msg' => '出库成功', 'data' => $result]);
            } catch (\Exception $e) {
                return json(['code' => 1, 'msg' => $e->getMessage()]);
            }
        }

        // GET 请求，显示出库页面
        $data = [];

        // 获取URL参数
        $productId = Request::get('product_id', 0);
        $warehouseId = Request::get('warehouse_id', 0);

        // 如果有产品ID，获取产品信息
        if ($productId > 0) {
            $productModel = new \app\product\model\Product();
            $product = $productModel->find($productId);
            if ($product) {
                $data['product'] = $product->toArray();
            }
        }

        // 如果有仓库ID，获取仓库信息
        if ($warehouseId > 0) {
            $warehouseModel = new \app\warehouse\model\Warehouse();
            $warehouse = $warehouseModel->find($warehouseId);
            if ($warehouse) {
                $data['warehouse'] = $warehouse->toArray();
            }
        }

        // 获取所有产品列表（用于下拉选择）
        $productModel = new \app\product\model\Product();
        $products = $productModel->where('status', 1)->field('id,title,material_code,unit,specs')->select();
        $data['products'] = $products->toArray();

        // 获取所有仓库列表（用于下拉选择）
        $warehouseModel = new \app\warehouse\model\Warehouse();
        $warehouses = $warehouseModel->where('status', 1)->field('id,name,code')->select();
        $data['warehouses'] = $warehouses->toArray();

        return View::fetch('inventory_realtime/outbound', $data);
    }
    
    /**
     * 库存调拨
     */
    public function transfer()
    {
        if (Request::isPost()) {
            $params = Request::post();
            
            // 验证必填字段
            $validate = [
                'product_id' => 'require|integer|gt:0',
                'from_warehouse_id' => 'require|integer|gt:0',
                'to_warehouse_id' => 'require|integer|gt:0',
                'quantity' => 'require|float|gt:0'
            ];
            
            $this->validate($params, $validate);
            
            try {
                // 暂时简化调拨操作
                return json(['code' => 1, 'msg' => '调拨功能暂未实现']);
                
                return json(['code' => 0, 'msg' => '调拨成功', 'data' => $result]);
            } catch (\Exception $e) {
                return json(['code' => 1, 'msg' => $e->getMessage()]);
            }
        }

        // GET 请求，显示调拨页面
        $data = [];

        // 获取URL参数
        $productId = Request::get('product_id', 0);
        $warehouseId = Request::get('warehouse_id', 0);

        // 如果有产品ID，获取产品信息
        if ($productId > 0) {
            $productModel = new \app\product\model\Product();
            $product = $productModel->find($productId);
            if ($product) {
                $data['product'] = $product->toArray();
            }
        }

        // 如果有仓库ID，获取仓库信息（作为源仓库）
        if ($warehouseId > 0) {
            $warehouseModel = new \app\warehouse\model\Warehouse();
            $warehouse = $warehouseModel->find($warehouseId);
            if ($warehouse) {
                $data['from_warehouse'] = $warehouse->toArray();
            }
        }

        // 获取所有产品列表（用于下拉选择）
        $productModel = new \app\product\model\Product();
        $products = $productModel->where('status', 1)->field('id,title,material_code,unit,specs')->select();
        $data['products'] = $products->toArray();

        // 获取所有仓库列表（用于下拉选择）
        $warehouseModel = new \app\warehouse\model\Warehouse();
        $warehouses = $warehouseModel->where('status', 1)->field('id,name,code')->select();
        $data['warehouses'] = $warehouses->toArray();

        return View::fetch('inventory_realtime/transfer', $data);
    }
    
    /**
     * 锁定库存
     */
    public function lock()
    {
        if (Request::isPost()) {
            $params = Request::post();
            
            // 验证必填字段
            $validate = [
                'product_id' => 'require|integer|gt:0',
                'warehouse_id' => 'require|integer|gt:0',
                'quantity' => 'require|float|gt:0',
                'ref_type' => 'require',
                'ref_id' => 'require|integer|gt:0'
            ];
            
            $this->validate($params, $validate);
            
            try {
                // 暂时简化锁定操作
                return json(['code' => 1, 'msg' => '锁定功能暂未实现']);
                
                return json(['code' => 0, 'msg' => '锁定成功', 'data' => $result]);
            } catch (\Exception $e) {
                return json(['code' => 1, 'msg' => $e->getMessage()]);
            }
        }

        // GET 请求，显示锁定页面
        $data = [];

        // 获取URL参数
        $productId = Request::get('product_id', 0);
        $warehouseId = Request::get('warehouse_id', 0);

        // 如果有产品ID，获取产品信息
        if ($productId > 0) {
            $productModel = new \app\product\model\Product();
            $product = $productModel->find($productId);
            if ($product) {
                $data['product'] = $product->toArray();
            }
        }

        // 如果有仓库ID，获取仓库信息
        if ($warehouseId > 0) {
            $warehouseModel = new \app\warehouse\model\Warehouse();
            $warehouse = $warehouseModel->find($warehouseId);
            if ($warehouse) {
                $data['warehouse'] = $warehouse->toArray();
            }
        }

        // 获取所有产品列表（用于下拉选择）
        $productModel = new \app\product\model\Product();
        $products = $productModel->where('status', 1)->field('id,title,material_code,unit,specs')->select();
        $data['products'] = $products->toArray();

        // 获取所有仓库列表（用于下拉选择）
        $warehouseModel = new \app\warehouse\model\Warehouse();
        $warehouses = $warehouseModel->where('status', 1)->field('id,name,code')->select();
        $data['warehouses'] = $warehouses->toArray();

        return View::fetch('inventory_realtime/lock', $data);
    }
    
    /**
     * 释放锁定
     */
    public function unlock()
    {
        if (Request::isPost()) {
            $params = Request::post();
            
            // 验证必填字段
            $validate = [
                'product_id' => 'require|integer|gt:0',
                'warehouse_id' => 'require|integer|gt:0',
                'quantity' => 'require|float|gt:0',
                'ref_type' => 'require',
                'ref_id' => 'require|integer|gt:0'
            ];
            
            $this->validate($params, $validate);
            
            try {
                // 暂时简化释放锁定操作
                return json(['code' => 1, 'msg' => '释放锁定功能暂未实现']);
                
                return json(['code' => 0, 'msg' => '释放成功', 'data' => $result]);
            } catch (\Exception $e) {
                return json(['code' => 1, 'msg' => $e->getMessage()]);
            }
        }

        // GET 请求，显示释放锁定页面
        $data = [];

        // 获取URL参数
        $productId = Request::get('product_id', 0);
        $warehouseId = Request::get('warehouse_id', 0);

        // 如果有产品ID，获取产品信息
        if ($productId > 0) {
            $productModel = new \app\product\model\Product();
            $product = $productModel->find($productId);
            if ($product) {
                $data['product'] = $product->toArray();
            }
        }

        // 如果有仓库ID，获取仓库信息
        if ($warehouseId > 0) {
            $warehouseModel = new \app\warehouse\model\Warehouse();
            $warehouse = $warehouseModel->find($warehouseId);
            if ($warehouse) {
                $data['warehouse'] = $warehouse->toArray();
            }
        }

        // 获取所有产品列表（用于下拉选择）
        $productModel = new \app\product\model\Product();
        $products = $productModel->where('status', 1)->field('id,title,material_code,unit,specs')->select();
        $data['products'] = $products->toArray();

        // 获取所有仓库列表（用于下拉选择）
        $warehouseModel = new \app\warehouse\model\Warehouse();
        $warehouses = $warehouseModel->where('status', 1)->field('id,name,code')->select();
        $data['warehouses'] = $warehouses->toArray();

        return View::fetch('inventory_realtime/unlock', $data);
    }
    
    /**
     * 使用锁定库存
     */
    public function useLocked()
    {
        if (Request::isPost()) {
            $params = Request::post();
            
            // 验证必填字段
            $validate = [
                'product_id' => 'require|integer|gt:0',
                'warehouse_id' => 'require|integer|gt:0',
                'quantity' => 'require|float|gt:0',
                'ref_type' => 'require',
                'ref_id' => 'require|integer|gt:0'
            ];
            
            $this->validate($params, $validate);
            
            try {
                // 暂时简化使用锁定库存操作
                return json(['code' => 1, 'msg' => '使用锁定库存功能暂未实现']);
                
                return json(['code' => 0, 'msg' => '使用成功', 'data' => $result]);
            } catch (\Exception $e) {
                return json(['code' => 1, 'msg' => $e->getMessage()]);
            }
        }

        // GET 请求，显示使用锁定库存页面
        $data = [];

        // 获取URL参数
        $productId = Request::get('product_id', 0);
        $warehouseId = Request::get('warehouse_id', 0);

        // 如果有产品ID，获取产品信息
        if ($productId > 0) {
            $productModel = new \app\product\model\Product();
            $product = $productModel->find($productId);
            if ($product) {
                $data['product'] = $product->toArray();
            }
        }

        // 如果有仓库ID，获取仓库信息
        if ($warehouseId > 0) {
            $warehouseModel = new \app\warehouse\model\Warehouse();
            $warehouse = $warehouseModel->find($warehouseId);
            if ($warehouse) {
                $data['warehouse'] = $warehouse->toArray();
            }
        }

        // 获取所有产品列表（用于下拉选择）
        $productModel = new \app\product\model\Product();
        $products = $productModel->where('status', 1)->field('id,title,material_code,unit,specs')->select();
        $data['products'] = $products->toArray();

        // 获取所有仓库列表（用于下拉选择）
        $warehouseModel = new \app\warehouse\model\Warehouse();
        $warehouses = $warehouseModel->where('status', 1)->field('id,name,code')->select();
        $data['warehouses'] = $warehouses->toArray();

        return View::fetch('inventory_realtime/use_locked', $data);
    }
    
    /**
     * 库存汇总
     */
    public function summary()
    {
        $productId = Request::param('product_id');

        if (!$productId) {
            return json(['code' => 1, 'msg' => '产品ID不能为空']);
        }

        try {
            $summary = $this->model->getProductSummary($productId);
            return json(['code' => 0, 'msg' => '查询成功', 'data' => $summary]);
        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 库存预警
     */
    public function alerts()
    {
        if (Request::isAjax()) {
            // 搜索条件
            $filter = Request::get();

            // 查询库存预警数据（库存量低于预警值的产品）
            $list = $this->model
                ->alias('ir')
                ->leftJoin('warehouse w', 'ir.warehouse_id = w.id')
                ->where('ir.available_quantity', '<=', 10) // 默认预警值为10
                ->field('ir.*, w.name as warehouse_name')
                ->order('ir.available_quantity asc')
                ->paginate([
                    'list_rows' => $filter['limit'] ?? 10,
                    'query' => $filter
                ]);

            return json(['code' => 0, 'msg' => '', 'count' => $list->total(), 'data' => $list->items()]);
        }

        return View::fetch('inventory_realtime/alerts');
    }

    /**
     * 库存统计
     */
    public function statistics()
    {
        if (Request::isAjax()) {
            // 搜索条件
            $filter = Request::get();

            // 查询库存统计数据
            $list = $this->model
                ->alias('ir')
                ->leftJoin('warehouse w', 'ir.warehouse_id = w.id')
                ->field('
                    w.name as warehouse_name,
                    COUNT(ir.id) as product_count,
                    SUM(ir.quantity) as total_quantity,
                    SUM(ir.available_quantity) as total_available,
                    SUM(ir.locked_quantity) as total_locked,
                    SUM(ir.quantity * ir.cost_price) as total_value
                ')
                ->group('ir.warehouse_id')
                ->order('total_value desc')
                ->paginate([
                    'list_rows' => $filter['limit'] ?? 10,
                    'query' => $filter
                ]);

            return json(['code' => 0, 'msg' => '', 'count' => $list->total(), 'data' => $list->items()]);
        }

        return View::fetch('inventory_realtime/statistics');
    }
}
