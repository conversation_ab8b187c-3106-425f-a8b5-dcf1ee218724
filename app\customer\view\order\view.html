{extend name="../../base/view/common/base" /}

{block name="style"}
<style>
.layui-tab-title .layui-this{background-color:#fff;}
.layui-tab-card,.layui-card{box-shadow:0 0 0 0 rgb(0 0 0 / 10%); border-radius:0; border-top:none;}
.check-status-color-0{color:#FF9800;}
.check-status-color-1{color:#2196F3;}
.check-status-color-2{color:#4CAF50;}
.check-status-color-3{color:#F44336;}
.check-status-color-4{color:#4CAF50;}
.check-status-color-5{color:#9E9E9E;}
/* BOM弹窗样式 */
.bom-detail-table {width: 100%; border-collapse: collapse;}
.bom-detail-table th, .bom-detail-table td {border: 1px solid #e6e6e6; padding: 8px; text-align: center;}
.bom-detail-table th {background-color: #f2f2f2;}
.bom-toggle {cursor: pointer; color: #1E9FFF;}
.bom-toggle:hover {text-decoration: underline;}
.bom-shortage {color: #F44336; font-weight: bold;}
/* 父子商品关系样式 - 增强版 */
.child-product {background-color: #f8f8f8; border-left: 3px solid #1E9FFF;}
.child-product-name {
    padding-left: 30px; 
    position: relative;
    color: #333;
    font-weight: normal;
}
.child-product-name:before {
    content: "└─";
    position: absolute;
    left: 8px;
    color: #1E9FFF;
    font-weight: bold;
    font-size: 16px;
}
/* 父商品标记 */
.parent-product-marker {
    display: inline-block;
    background-color: #1E9FFF;
    color: white;
    font-size: 12px;
    padding: 0 5px;
    border-radius: 3px;
    margin-right: 5px;
    vertical-align: middle;
}
</style>
{/block}

{block name="body"}
<div class="p-page">
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
        <h3>订单详情</h3>
        <button type="button" class="layui-btn layui-btn-primary" id="btnBack">返回</button>
    </div>
    <table class="layui-table layui-table-form">
        <tr>
            <td class="layui-td-gray">订单编号</td>
            <td>{$order.order_no|default=''}</td>
            <td class="layui-td-gray">订单状态</td>
            <td>
                <span class="check-status-color-{$order.status|default='0'}">『{$order.status_text|default='待审核'}』</span>
            </td>
        </tr>
        <tr>
            <td class="layui-td-gray">客户名称</td>
            <td>{$order.customer.name|default=''}</td>
            <td class="layui-td-gray">订单类型</td>
            <td>{$order.order_type_text|default=''}</td>
        </tr>
        <tr>
            <td class="layui-td-gray">交货日期</td>
            <td>{$order.delivery_date|default=''}</td>
            <td class="layui-td-gray">税率</td>
            <td>{$order.tax_rate|default='0'}%</td>
        </tr>
        <tr>
            <td class="layui-td-gray">创建人</td>
            <td>{$order.adduser.name|default=''}</td>
            <td class="layui-td-gray">创建时间</td>
            <td>{$order.create_time_format|default=''}</td>
        </tr>
        <tr>
            <td class="layui-td-gray">管销费</td>
            <td>{$order.glf|default='0'}</td>
            <td class="layui-td-gray">运费</td>
            <td>{$order.yunfei|default='0'}</td>
        </tr>
        <tr>
            <td class="layui-td-gray">已付款</td>
            <td>{$order.pay_money|default='0'}</td>
            <td class="layui-td-gray"></td>
            <td style="color: red;">{if $order.pay_status<2 && $order.order_type == 1}未付款不可发货{else} {/if}</td>
        </tr>
        
        
        {if !empty($order.remark)}
        <tr>
            <td class="layui-td-gray">备注说明</td>
            <td colspan="3">{$order.remark}</td>
        </tr>
        {/if}
        <tr>
            <td colspan="4"><strong>订单明细</strong></td>
        </tr>
        <tr>
            <td colspan="4" style="padding: 0;">
                <table class="layui-table">
                    <thead>
                        <tr>
                            <th style="text-align: center;">序号</th>
                            <th>材料编码</th>
                            <th>产品名称</th>
                            <th style="text-align: center;">数量</th>
                            <th style="text-align: center;">每套/件                            </th>
                            <th style="text-align: center;">单价</th>
                            <th style="text-align: center;">金额</th>
                            <th style="text-align: center;">税率</th>
                            <th style="text-align: center;">税额</th>
                            <th style="text-align: center;">库存</th>
                            <th style="text-align: center;">备注</th>
                            <th style="text-align: center;">BOM</th>
                            <th style="text-align: center;">来源</th>
                            <th style="text-align: center;">附件</th>
                        </tr>
                    </thead>
                    <tbody>
                        {foreach $items as $key => $item}
                        <tr class="{$item.is_child ? 'child-product' : ''}" 
                            data-product-id="{$item.product_id}"
                            {if $item.is_child}data-parent-id="{$item.parent_product_id}"{/if}>
                            {if !$item.is_child}
                                 <td style="text-align: center;">{$parent_index = isset($parent_index) ? $parent_index + 1 : 1}</td>
                                 {else}
                                 <td style="text-align: center;"> </td>
                            {/if}   

                            <td>{$item.material_code|default=''}</td>
                            <td class="{$item.is_child ? 'child-product-name' : ''}">
                                {if !$item.is_child}
                                    <span class="parent-product-marker">主品</span>
                                    {if $item.has_children}
                                        <span class="layui-badge layui-bg-green" title="包含{$item.child_count}个子商品">{$item.child_count}</span>
                                    {/if}
                                {else}
                                    <span class="layui-badge layui-bg-orange">子品</span>
                                    <span class="layui-badge-rim" title="所属父商品">{$item.parent_product_name|default=''}</span>
                                {/if}
                                {$item.product_name|default=''}
                            </td>
                            <td style="text-align: center;">{$item.quantity|default='0'}</td>
                            <td style="text-align: center;">
                                {if $item.is_child}
                                    {$item.ratio|number_format=2}
                                {else}
                                    -
                                {/if}
                            </td>
                            <td style="text-align: center;">{$item.unit_price|default='0'}</td>
                            <td style="text-align: center;">{$item.amount|default='0'}</td>
                            <td style="text-align: center;">{$item.tax_rate|default='0'}%</td>
                            <td style="text-align: center;">{$item.tax_amount|default='0'}</td>
                            <td style="text-align: center;">{$item.stock|default='0'}</td>
                            <td style="text-align: center;">{$item.remark|default=''}</td>
                            <td style="text-align: center;">
                                {if $item.has_bom == '有'}
                                <span class="bom-toggle" data-product-id="{$item.product_id}" data-order-qty="{$item.quantity}">
                                    <i class="layui-icon layui-icon-form"></i> {$item.has_bom}
                                </span>
                                {else}
                                {$item.has_bom|default=''}
                                {/if}
                            </td>
                            <td style="text-align: center;">{$item.source_type|default=''}</td>
                            <td style="text-align: center;">
                                {if !empty($item.attachment)}
                                {php}
                                $attachment_ids = explode(',', $item['attachment']);
                                foreach($attachment_ids as $aid) {
                                    $file = \think\facade\Db::name('file')->where('id', $aid)->find();
                                    if($file && !empty($file['filepath'])) {
                                        echo '<div class="layui-inline" style="margin-bottom:5px;">';
                                        echo '<img src="'.$file['filepath'].'" style="max-width:80px;max-height:80px;margin-right:5px;" onclick="previewImage(\''.$file['filepath'].'\')">';
                                        echo '</div>';
                                    }
                                }
                                {/php}
                                {else}
                                <span class="layui-badge layui-bg-gray">无附件</span>
                                {/if}
                            </td>
                        </tr>
                        {/foreach}
                    </tbody>
                    <tfoot>
                        <tr>
                            <td colspan="5" style="text-align: right;"><strong>合计(含税)：</strong></td>
                            <td style="text-align: right;"><strong>{$order.total_amount|default=$totalAmount}</strong></td>
                            <td>&nbsp;</td>
                            <td style="text-align: right;"> </td>
                            <td>&nbsp;</td>
                        </tr>
                    </tfoot>
                </table>
            </td>
        </tr>
    </table>
        <!-- 操作按钮 -->
        {gt name="$is_leader" value="0"}
        {if $order.status > 0 }
            <div class="layui-card">
                <div class="layui-card-body">
                    <div class="layui-btn-container">
                        {if $order.pay_status<2}
                            {if $order.order_type == 1}
                                <button class="layui-btn layui-btn-warm" id="reportException">同意未收款发货</button>
                            {/if}
                        {/if}

                    </div>
                </div>
            </div>
        {/if}
        {/gt}
    
    <div class="layui-tab layui-tab-card" style="margin:0; background-color:#fff;" lay-filter="purchase" id="purchaseTab">
        <ul class="layui-tab-title">
            <li class="layui-this">付款记录</li>
            <li >发货记录</li>
            <li >关联单据</li>
			<li >操作记录</li>
		</ul>
		<div class="layui-tab-content" style="padding:0;">
            <div class="layui-tab-item layui-show" >
				<div style="padding: 15px;">
					<table class="layui-table" lay-skin="line">
						<thead>
							<tr>
								<th width="80">序号</th>
								<th width="150">收款日期</th>
								<th width="120">支付类型</th>
								<th width="120">金额</th>
								<th width="120">到账状态</th>
								<th width="150">确认时间</th>
								<th width="150">操作时间</th>
								<th width="100">操作</th>
							</tr>
						</thead>
						<tbody id="paymentRecords">
							<!-- 付款记录数据将通过AJAX加载 -->
						</tbody>
					</table>
					<div id="noPaymentData" style="text-align: center; padding: 50px; color: #999; display: none;">
						<i class="layui-icon layui-icon-face-cry" style="font-size: 48px; color: #d2d2d2;"></i>
						<p style="margin-top: 15px;">暂无付款记录</p>
					</div>
				</div>
			</div>
            <div class="layui-tab-item" >
				<div style="padding: 15px;">
					<table class="layui-table" lay-skin="line">
						<thead>
							<tr>
								<th width="80">序号</th>
								<th width="180">发货单号</th>
								<th width="150">预计发货时间</th>
								<th width="120">联系人</th>
								<th width="200">收货地址</th>
								<th width="120">状态</th>
								<th width="150">操作时间</th>
								<th width="100">操作</th>
							</tr>
						</thead>
						<tbody id="deliveryRecords">
							<!-- 发货记录数据将通过AJAX加载 -->
						</tbody>
					</table>
					<div id="noDeliveryData" style="text-align: center; padding: 50px; color: #999; display: none;">
						<i class="layui-icon layui-icon-face-cry" style="font-size: 48px; color: #d2d2d2;"></i>
						<p style="margin-top: 15px;">暂无发货记录</p>
					</div>
				</div>
			</div>
            <div class="layui-tab-item">
				{include file="order/view_related" /}
			</div>
			<div class="layui-tab-item">
				{include file="order/view_log" /}
			</div>
		</div>
    </div>  
</div>

<!-- 隐藏的数据字段 -->
<input type="hidden" id="order_id" value="{$order.id}">
<input type="hidden" id="order_status" value="{$order.status}">
{/block}


<!-- 脚本 -->
{block name="script"}
<script>
    var order_id = "{$order.id|default=0}";
    var purchase_id = "{$order.id|default=0}";
    var auth = "{$auth|default=0}";
    var moduleInit = ['tool','oaPicker','oaEdit'];
    //var checking_btn = '<span class="layui-btn layui-btn-warm" data-event="stop" data-status="1">中止订单</span><span class="layui-btn layui-btn-danger" data-event="void" data-status="1">作废订单</span>';
    var checking_btn = '';
    
    // 图片预览函数 - 移到全局作用域
    function previewImage(filepath) {
        // 检查layer对象是否已定义
        if (typeof layer === 'undefined') {
            console.warn('layer对象未定义，使用备用预览方法');
            window.open(filepath, '_blank');
            return;
        }
        
        try {
            layer.photos({
                photos: {
                    "data": [{"src": filepath}]
                },
                anim: 5,
                shade: 0.8,
                closeBtn: 1,
                shadeClose: true
            });
        } catch (e) {
            console.error("图片预览错误:", e, filepath);
            // 如果上述方法失败，直接在新窗口打开
            window.open(filepath, '_blank');
        }
    }
    
    function gouguInit() {
        var $ = layui.jquery,
            tool = layui.tool,
            layer = layui.layer,
            element = layui.element,
            oaPicker = layui.oaPicker,
            tips = layui.tips,
            oaEdit = layui.oaEdit;

        // 付款记录加载函数
        window.loadPaymentLog = function() {
            var orderId = $('#order_id').val();
            if (!orderId) {
                $('#noPaymentData').show();
                return;
            }

            // 显示加载状态
            $('#paymentRecords').html('<tr><td colspan="8" style="text-align: center; padding: 50px;"><i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i> 加载中...</td></tr>');

            // 获取付款记录数据
            $.get('/api/index/get_list', {
                name: 'customer_order_payment',
                action_id: orderId,
                page: 1,
                limit: 50
            }, function(res) {
                if (res.code == 0 && res.data && res.data.length > 0) {
                    var html = '';
                    $.each(res.data, function(index, item) {
                        var typeText = item.type == 'payment' ? '<span class="layui-badge layui-bg-yellow">支付</span>' : '<span class="layui-badge layui-bg-black">退款</span>';
                        var statusText = '';
                        switch(item.status) {
                            case 0: statusText = '<span class="layui-badge layui-bg-gray">未受理</span>'; break;
                            case 1: statusText = '<span class="layui-badge layui-bg-green">已到账</span>'; break;
                            case 2: statusText = '<span class="layui-badge layui-bg-blue">失败</span>'; break;
                            default: statusText = '<span class="layui-badge layui-bg-gray">未知</span>';
                        }

                        html += '<tr>';
                        html += '<td>' + (index + 1) + '</td>';
                        html += '<td>' + (item.expense_time || '-') + '</td>';
                        html += '<td>' + typeText + '</td>';
                        html += '<td>' + (item.amount || '0') + '</td>';
                        html += '<td>' + statusText + '</td>';
                        html += '<td>' + formatDateTime(item.confirmed_at) + '</td>';
                        html += '<td>' + formatDateTime(item.created_at) + '</td>';
                        html += '<td><a href="javascript:void(0);" onclick="viewPaymentDetail(' + item.id + ')" class="layui-btn layui-btn-xs">查看</a></td>';
                        html += '</tr>';
                    });
                    $('#paymentRecords').html(html);
                    $('#noPaymentData').hide();
                } else {
                    $('#paymentRecords').html('');
                    $('#noPaymentData').show();
                }
            }).fail(function() {
                $('#paymentRecords').html('<tr><td colspan="8" style="text-align: center; padding: 50px; color: #f56c6c;">加载失败，请刷新重试</td></tr>');
            });
        };

        // 发货记录加载函数
        window.loadDeliveryLog = function() {
            var orderId = $('#order_id').val();
            if (!orderId) {
                $('#noDeliveryData').show();
                return;
            }

            // 显示加载状态
            $('#deliveryRecords').html('<tr><td colspan="8" style="text-align: center; padding: 50px;"><i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i> 加载中...</td></tr>');

            // 获取发货记录数据
            $.get('/api/index/get_list', {
                name: 'customer_delivery',
                action_id: orderId,
                page: 1,
                limit: 50
            }, function(res) {
                if (res.code == 0 && res.data && res.data.length > 0) {
                    var html = '';
                    $.each(res.data, function(index, item) {
                        var statusText = '';
                        switch(item.status) {
                            case 0: statusText = '<span class="layui-badge layui-bg-gray">待发货</span>'; break;
                            case 1: statusText = '<span class="layui-badge layui-bg-green">已出库</span>'; break;
                            case 2: statusText = '<span class="layui-badge layui-bg-blue">已完成</span>'; break;
                            case 3: statusText = '<span class="layui-badge layui-bg-black">已取消</span>'; break;
                            default: statusText = '<span class="layui-badge layui-bg-gray">未知</span>';
                        }

                        html += '<tr>';
                        html += '<td>' + (index + 1) + '</td>';
                        html += '<td><a href="javascript:void(0);" onclick="viewDeliveryDetail(' + item.id + ')" style="color: #1E9FFF;">' + (item.delivery_no || '-') + '</a></td>';
                        html += '<td>' + (item.expect_time || '-') + '</td>';
                        html += '<td>' + (item.contact || '-') + '</td>';
                        html += '<td>' + (item.address || '-') + '</td>';
                        html += '<td>' + statusText + '</td>';
                        html += '<td>' + (item.create_time || '-') + '</td>';
                        html += '<td><a href="javascript:void(0);" onclick="viewDeliveryDetail(' + item.id + ')" class="layui-btn layui-btn-xs">查看</a></td>';
                        html += '</tr>';
                    });
                    $('#deliveryRecords').html(html);
                    $('#noDeliveryData').hide();
                } else {
                    $('#deliveryRecords').html('');
                    $('#noDeliveryData').show();
                }
            }).fail(function() {
                $('#deliveryRecords').html('<tr><td colspan="8" style="text-align: center; padding: 50px; color: #f56c6c;">加载失败，请刷新重试</td></tr>');
            });
        };

        // 关联单据相关函数
        window.loadRelatedDocuments = function() {
            var orderId = $('#order_id').val();
            if (!orderId) {
                $('#noRelatedData').show();
                return;
            }

            // 显示加载状态
            $('#relatedDocuments').html('<tr><td colspan="8" style="text-align: center; padding: 50px;"><i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i> 加载中...</td></tr>');

            // 获取关联单据数据
            $.get('/customer/order/getRelatedDocuments', {id: orderId}, function(res) {
                if (res.code == 0 && res.data && res.data.length > 0) {
                    var html = '';
                    $.each(res.data, function(index, item) {
                        var statusClass = getStatusClass(item.type, item.status);
                        var statusText = getStatusText(item.type, item.status);

                        html += '<tr>';
                        html += '<td>' + (index + 1) + '</td>';
                        html += '<td>' + item.type_text + '</td>';
                        html += '<td><a href="javascript:void(0);" onclick="viewDocument(\'' + item.type + '\', ' + item.id + ')" style="color: #1E9FFF;">' + item.document_no + '</a></td>';
                        html += '<td><span class="layui-badge ' + statusClass + '">' + statusText + '</span></td>';
                        html += '<td>' + (item.product_name || '-') + '</td>';
                        html += '<td>' + (item.creator_name || '-') + '</td>';
                        html += '<td>' + formatDateTime(item.create_time) + '</td>';
                        html += '<td><a href="javascript:void(0);" onclick="viewDocument(\'' + item.type + '\', ' + item.id + ')" class="layui-btn layui-btn-xs">查看</a></td>';
                        html += '</tr>';
                    });
                    $('#relatedDocuments').html(html);
                    $('#noRelatedData').hide();
                } else {
                    $('#relatedDocuments').html('');
                    $('#noRelatedData').show();
                }
            }).fail(function() {
                $('#relatedDocuments').html('<tr><td colspan="8" style="text-align: center; padding: 50px; color: #f56c6c;">加载失败，请刷新重试</td></tr>');
            });
        };

        // 获取状态样式类
        window.getStatusClass = function(type, status) {
            var statusMap = {
                'purchase_order': {
                    0: 'layui-bg-gray',
                    1: 'layui-bg-blue',
                    2: 'layui-bg-green',
                    3: 'layui-bg-red'
                },
                'material_requirement': {
                    0: 'layui-bg-gray',
                    1: 'layui-bg-blue',
                    2: 'layui-bg-green'
                },
                'inventory_transfer': {
                    0: 'layui-bg-gray',
                    1: 'layui-bg-blue',
                    2: 'layui-bg-green',
                    3: 'layui-bg-red'
                },
                'delivery': {
                    0: 'layui-bg-gray',
                    1: 'layui-bg-green'
                },
                'invoice': {
                    0: 'layui-bg-gray',
                    1: 'layui-bg-green'
                }
            };

            return statusMap[type] && statusMap[type][status] ? statusMap[type][status] : 'layui-bg-gray';
        };

        // 获取状态文本
        window.getStatusText = function(type, status) {
            var statusMap = {
                'purchase_order': {
                    1: '草稿',
                    2: '待审核',
                    3: '已审核',
                    4: '部分入库',
                    5: '已完成',
                    6:'已取消'
                },
                'material_requirement': {
                    0: '未处理',
                    1: '处理中',
                    2: '已完成'
                },
                'inventory_transfer': {
                    0: '待审核',
                    1: '已审核',
                    2: '已完成',
                    3: '已取消'
                },
                'delivery': {
                    0: '待发货',
                    1: '已发货'
                },
                'invoice': {
                    0: '未开票',
                    1: '已开票'
                }
            };

            return statusMap[type] && statusMap[type][status] ? statusMap[type][status] : '未知';
        };

        // 查看单据详情
        window.viewDocument = function(type, id) {
            var urlMap = {
                'purchase_order': '/purchase/order/detail.html?id=' + id,
                'material_requirement': '/warehouse/MaterialRequirement/view?id=' + id,
                'inventory_transfer': '/warehouse/InventoryTransfer/view?id=' + id,
                'delivery': '/customer/delivery/view?id=' + id,
                'invoice': '/finance/invoice/view?id=' + id
            };

            var url = urlMap[type];
            if (url) {
                if (typeof tool !== 'undefined' && tool.side) {
                    tool.side(url);
                } else {
                    window.open(url, '_blank');
                }
            } else {
                layer.msg('暂不支持查看此类型单据', {icon: 2});
            }
        };

        // 格式化日期时间
        window.formatDateTime = function(timestamp) {
            if (!timestamp) return '-';
            var date = new Date(timestamp * 1000);
            var year = date.getFullYear();
            var month = String(date.getMonth() + 1).padStart(2, '0');
            var day = String(date.getDate()).padStart(2, '0');
            var hours = String(date.getHours()).padStart(2, '0');
            var minutes = String(date.getMinutes()).padStart(2, '0');
            var seconds = String(date.getSeconds()).padStart(2, '0');

            return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
        };

        // 查看付款详情
        window.viewPaymentDetail = function(id) {
            if (typeof tool !== 'undefined' && tool.side) {
                tool.side('/customer/payment/view?id=' + id);
            } else {
                layer.open({
                    type: 2,
                    title: '付款详情',
                    shadeClose: true,
                    shade: 0.8,
                    area: ['80%', '80%'],
                    content: '/customer/payment/view?id=' + id
                });
            }
        };

        // 查看发货详情
        window.viewDeliveryDetail = function(id) {
            if (typeof tool !== 'undefined' && tool.side) {
                tool.side('/customer/delivery/view?id=' + id);
            } else {
                layer.open({
                    type: 2,
                    title: '发货详情',
                    shadeClose: true,
                    shade: 0.8,
                    area: ['80%', '80%'],
                    content: '/customer/delivery/view?id=' + id
                });
            }
        };


        // Tab切换事件
        element.on('tab(purchase)', function(data){
            let index = data.index;
            if(index == 0){ // 付款记录选项卡
                loadPaymentLog();
            }else if(index === 1){ // 发货记录选项卡
                loadDeliveryLog();
            } else if(index === 2){ // 关联单据选项卡
                loadRelatedDocuments();
            } else if(index === 3){ // 操作记录选项卡
                log();
            }
        });
        
        // 报告异常按钮事件
        $('#reportException').on('click', function() {
            layer.confirm('确定要同意未收款发货吗,一旦操作不可撤销？', {
                icon: 3,
                title: '提示',
                area: ['400px', '150px']
            }, function(index) {
                let url = 'Delivery_without_payment';
                let postData = {
                    id: order_id
                };
                layer.prompt({
                    formType: 2,
                    title: '请输入同意未收款发货原因',
                    area: ['300px', '150px']
                }, function(value, promptIndex) {
                    postData.remark = value;
                    submitAction(url, postData);
                    layer.close(promptIndex);
                });
                
                layer.close(index);
            });
        });
        
        // 中止和作废订单操作
        $('body').on('click', '[data-event]', function() {
            let event = $(this).data('event');
            let status = $(this).data('status');
            let tips = '';
            
            if(event == 'stop') {
                tips = status == 1 ? '确定要中止此订单吗？' : '确定要取消中止此订单吗？';
            } else if(event == 'void') {
                tips = status == 1 ? '确定要作废此订单吗？' : '确定要取消作废此订单吗？';
            }
            
            if(tips) {
                layer.confirm(tips, {
                    icon: 3,
                    title: '提示'
                }, function(index) {
                    let url = `/purchase/order/${event}`;
                    let postData = {
                        id: order_id,
                        status: status
                    };
                    
                    if(status == 1) {
                        layer.prompt({
                            formType: 2,
                            title: '请输入' + (event == 'stop' ? '中止' : '作废') + '原因',
                            area: ['300px', '150px']
                        }, function(value, promptIndex) {
                            postData.remark = value;
                            submitAction(url, postData);
                            layer.close(promptIndex);
                        });
                    } else {
                        submitAction(url, postData);
                    }
                    
                    layer.close(index);
                });
            }
        });
        
        function submitAction(url, data) {
            tool.post(url, data, function(res) {
                if(res.code == 0) {
                    layer.msg(res.msg, {icon: 1});
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            });
        }

        // BOM展开功能
        $('.bom-toggle').on('click', function() {
            var productId = $(this).data('product-id');
            var orderQty = $(this).data('order-qty');
            var $this = $(this);
            var loadingIndex = layer.load(1); // 显示加载中
            
            // 加载BOM数据
            tool.get('/api/test/getBomInfo', {id: productId}, function(res) {
                layer.close(loadingIndex); // 关闭加载中
                
                if (res.code == 0 && res.data) {
                    var bomData = res.data;
                    var html = '<div style="padding: 15px;"><table class="bom-detail-table">';
                    html += '<thead><tr>';
                    html += '<th>序号</th>';
                    html += '<th>物料编码</th>';
                    html += '<th>物料名称</th>';
                    html += '<th>规格型号</th>';
                    html += '<th>单位</th>';
                    html += '<th>单位用量</th>';
                    html += '<th>订单用量</th>';
                    html += '<th>当前库存</th>';
                    html += '<th>缺口数量</th>';
                    html += '</tr></thead><tbody>';
                    
                    if (bomData.items && bomData.items.length > 0) {
                        bomData.items.forEach(function(item, index) {
                            // 计算订单用量 = 单位用量 × 订单数量
                            var totalQty = (parseFloat(item.qty) * parseFloat(orderQty)).toFixed(2);
                            // 计算缺口 = 订单用量 - 当前库存
                            var stock = item.stock || 0;
                            var shortage = totalQty - stock;
                            
                            html += '<tr>';
                            html += '<td>' + (index + 1) + '</td>';
                            html += '<td>' + (item.material_code || '') + '</td>';
                            html += '<td>' + (item.material_name || '') + '</td>';
                            html += '<td>' + (item.product_specs || '') + '</td>';
                            html += '<td>' + (item.uom_name || '') + '</td>';
                            html += '<td>' + (item.qty || '0') + '</td>';
                            html += '<td>' + totalQty + '</td>';
                            html += '<td>' + stock + '</td>';
                            html += '<td class="' + (shortage > 0 ? 'bom-shortage' : '') + '">' + 
                                   (shortage > 0 ? shortage.toFixed(2) : '0') + '</td>';
                            html += '</tr>';
                        });
                    } else {
                        html += '<tr><td colspan="9">暂无BOM数据</td></tr>';
                    }
                    
                    html += '</tbody></table></div>';
                    
                    // 使用layer.open创建弹出窗口
                    layer.open({
                        type: 1,
                        title: 'BOM清单详情',
                        area: ['80%', '800px'], // 设置弹窗宽高
                        shadeClose: true, // 点击遮罩关闭
                        maxmin: true, // 允许最大化最小化
                        content: html, // 弹窗内容
                        success: function(layero, index) {
                            // 弹窗创建成功后的回调
                            console.log('BOM弹窗已创建，ID: ' + index);
                        }
                    });
                } else {
                    layer.msg('加载BOM数据失败: ' + (res.msg || '未知错误'), {icon: 2});
                }
            });
        });
        

        
        // 删除按钮
        $('#btnDelete').click(function() {
            layer.confirm('确定要删除此订单吗？', {icon: 3, title:'提示'}, function(index){
                tool.post('/purchase/order/delete', {id: order_id}, function(res) {
                    if(res.code == 0) {
                        layer.msg(res.msg, {icon: 1});
                        setTimeout(function() {
                            tool.back();
                        }, 1000);
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                });
                layer.close(index);
            });
        });
        
        // 取消按钮
        $('#btnCancel').click(function() {
            layer.confirm('确定要取消此订单吗？', {icon: 3, title:'提示'}, function(index){
                layer.prompt({
                    formType: 2,
                    title: '请输入取消原因',
                    area: ['300px', '150px']
                }, function(value, promptIndex) {
                    tool.post('/purchase/order/cancel', {id: order_id, remark: value}, function(res) {
                        if(res.code == 0) {
                            layer.msg(res.msg, {icon: 1});
                            setTimeout(function() {
                                location.reload();
                            }, 1000);
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    });
                    layer.close(promptIndex);
                });
                layer.close(index);
            });
        });
        
        // 查看收货单
        $('.viewReceipt').click(function() {
            let receiptId = $(this).data('id');
            tool.side('/warehouse/Receipt/detail.html?id=' + receiptId);
        });
        

    }

    // 查看附件
    function viewAttachment(fileId, productName) {
        if (!fileId) {
            layer.msg('未找到附件');
            return;
        }
        
        $.ajax({
            url: '/api/index/get_file_info',
            type: 'get',
            data: {file_id: fileId},
            success: function(res) {
                if (res.code == 0 && res.data) {
                    var fileInfo = res.data;
                    var content = '<div style="padding: 20px;">' +
                        '<div class="layui-form-item">' +
                        '<label class="layui-form-label">商品</label>' +
                        '<div class="layui-input-block">' +
                        '<input type="text" class="layui-input" value="' + productName + '" readonly>' +
                        '</div></div>' +
                        '<div class="layui-form-item">' +
                        '<label class="layui-form-label">文件名</label>' +
                        '<div class="layui-input-block">' +
                        '<input type="text" class="layui-input" value="' + fileInfo.name + '" readonly>' +
                        '</div></div>' +
                        '<div class="layui-form-item">' +
                        '<label class="layui-form-label">文件类型</label>' +
                        '<div class="layui-input-block">' +
                        '<input type="text" class="layui-input" value="' + fileInfo.fileext + '" readonly>' +
                        '</div></div>' +
                        '<div class="layui-form-item" style="margin-top: 20px; text-align: center;">' +
                        '<a class="layui-btn layui-btn-normal" href="' + fileInfo.filepath + '" target="_blank">' +
                        '<i class="layui-icon layui-icon-download-circle"></i> 下载文件</a>' +
                        '</div></div>';
                        
                    layer.open({
                        type: 1,
                        title: '附件信息',
                        content: content,
                        area: ['500px', '350px'],
                        shade: 0.3,
                        closeBtn: 1,
                        shadeClose: true
                    });
                } else {
                    layer.msg(res.msg || '获取文件信息失败');
                }
            },
            error: function() {
                layer.msg('请求失败，请重试');
            }
        });

        // 页面加载完成后，默认加载付款记录
        $(document).ready(function() {
            loadPaymentLog();
        });
    }
</script>
{/block}
<!-- /脚本 -->
