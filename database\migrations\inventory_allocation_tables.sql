-- 库存分配与锁定一体化系统数据库表结构
-- 创建时间: 2025-08-05
-- 说明: 支持多业务场景的库存分配与锁定管理

-- 1. 库存分配需求表
CREATE TABLE IF NOT EXISTS `oa_inventory_allocation_request` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `product_id` int(11) NOT NULL COMMENT '产品ID',
  `warehouse_id` int(11) NOT NULL COMMENT '仓库ID',
  `quantity` decimal(10,2) NOT NULL COMMENT '需求数量',
  `allocated_quantity` decimal(10,2) DEFAULT 0.00 COMMENT '已分配数量',
  `ref_type` varchar(50) NOT NULL COMMENT '业务类型(customer_order,production_order,purchase_order,transfer,quality_check)',
  `ref_id` int(11) NOT NULL COMMENT '业务ID',
  `ref_no` varchar(100) DEFAULT '' COMMENT '业务单号',
  `priority` int(11) DEFAULT 50 COMMENT '优先级(数值越大优先级越高)',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态:1待分配,2部分分配,3完全分配,4已取消',
  `request_time` int(11) NOT NULL COMMENT '请求时间',
  `notes` text COMMENT '备注信息',
  `created_by` int(11) DEFAULT 0 COMMENT '创建人ID',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_product_warehouse` (`product_id`, `warehouse_id`),
  KEY `idx_priority_time` (`priority` DESC, `request_time` ASC),
  KEY `idx_status` (`status`),
  KEY `idx_ref` (`ref_type`, `ref_id`),
  KEY `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存分配需求表';

-- 2. 反审操作日志表
CREATE TABLE IF NOT EXISTS `oa_reverse_audit_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `ref_type` varchar(50) NOT NULL COMMENT '业务类型(customer_order,production_order,purchase_order等)',
  `ref_id` int(11) NOT NULL COMMENT '业务ID',
  `ref_no` varchar(100) DEFAULT '' COMMENT '业务单号',
  `released_locks` text COMMENT '释放的锁定记录JSON格式',
  `released_quantity` decimal(10,2) DEFAULT 0.00 COMMENT '释放的总数量',
  `reason` varchar(500) DEFAULT '' COMMENT '反审原因',
  `operator_id` int(11) NOT NULL COMMENT '操作人ID',
  `operator_name` varchar(100) DEFAULT '' COMMENT '操作人姓名',
  `create_time` int(11) NOT NULL COMMENT '操作时间',
  PRIMARY KEY (`id`),
  KEY `idx_ref` (`ref_type`, `ref_id`),
  KEY `idx_operator` (`operator_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='反审操作日志表';

-- 3. 库存分配历史记录表
CREATE TABLE IF NOT EXISTS `oa_inventory_allocation_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `request_id` int(11) NOT NULL COMMENT '分配需求ID',
  `product_id` int(11) NOT NULL COMMENT '产品ID',
  `warehouse_id` int(11) NOT NULL COMMENT '仓库ID',
  `allocated_quantity` decimal(10,2) NOT NULL COMMENT '本次分配数量',
  `lock_id` int(11) DEFAULT 0 COMMENT '对应的锁定记录ID',
  `allocation_type` varchar(20) DEFAULT 'auto' COMMENT '分配类型:auto自动分配,manual手动分配',
  `allocation_source` varchar(50) DEFAULT '' COMMENT '分配来源:inbound入库分配,adjustment调整分配',
  `notes` text COMMENT '分配备注',
  `created_by` int(11) DEFAULT 0 COMMENT '分配操作人',
  `create_time` int(11) NOT NULL COMMENT '分配时间',
  PRIMARY KEY (`id`),
  KEY `idx_request_id` (`request_id`),
  KEY `idx_product_warehouse` (`product_id`, `warehouse_id`),
  KEY `idx_lock_id` (`lock_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存分配历史记录表';

-- 4. 优化现有库存锁定表索引
-- 检查并添加缺失的索引
ALTER TABLE `oa_inventory_lock` 
ADD INDEX IF NOT EXISTS `idx_ref_status` (`ref_type`, `ref_id`, `status`),
ADD INDEX IF NOT EXISTS `idx_product_warehouse_status` (`product_id`, `warehouse_id`, `status`),
ADD INDEX IF NOT EXISTS `idx_create_time` (`create_time`);

-- 5. 库存分配配置表
CREATE TABLE IF NOT EXISTS `oa_inventory_allocation_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键名',
  `config_value` text NOT NULL COMMENT '配置值(JSON格式)',
  `config_desc` varchar(500) DEFAULT '' COMMENT '配置说明',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用:1启用,0禁用',
  `created_by` int(11) DEFAULT 0 COMMENT '创建人',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存分配配置表';

-- 6. 插入默认配置数据
INSERT INTO `oa_inventory_allocation_config` (`config_key`, `config_value`, `config_desc`, `create_time`, `update_time`) VALUES
('priority_weights', '{"emergency_production":100,"normal_production":90,"vip_customer_order":80,"urgent_customer_order":70,"normal_customer_order":60,"transfer_order":50,"quality_check":40}', '业务类型优先级权重配置', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('allocation_rules', '{"enable_partial_allocation":true,"min_allocation_ratio":0.1,"max_wait_days":30,"auto_cancel_expired":true}', '分配规则配置', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('notification_settings', '{"enable_allocation_notify":true,"enable_shortage_alert":true,"alert_threshold_days":7}', '通知设置配置', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 7. 创建视图：库存分配汇总视图
CREATE OR REPLACE VIEW `v_inventory_allocation_summary` AS
SELECT 
    r.product_id,
    r.warehouse_id,
    r.ref_type,
    COUNT(*) as request_count,
    SUM(r.quantity) as total_request_quantity,
    SUM(r.allocated_quantity) as total_allocated_quantity,
    SUM(r.quantity - r.allocated_quantity) as pending_quantity,
    AVG(r.priority) as avg_priority,
    MIN(r.request_time) as earliest_request_time,
    MAX(r.request_time) as latest_request_time
FROM `oa_inventory_allocation_request` r
WHERE r.status IN (1, 2) -- 待分配和部分分配
GROUP BY r.product_id, r.warehouse_id, r.ref_type;

-- 8. 创建存储过程：清理过期的分配需求
DELIMITER $$
CREATE PROCEDURE `sp_cleanup_expired_allocation_requests`()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE expired_count INT DEFAULT 0;
    
    -- 获取配置的最大等待天数
    DECLARE max_wait_days INT DEFAULT 30;
    
    SELECT JSON_EXTRACT(config_value, '$.max_wait_days') INTO max_wait_days
    FROM oa_inventory_allocation_config 
    WHERE config_key = 'allocation_rules' AND is_active = 1
    LIMIT 1;
    
    -- 更新过期的分配需求状态为已取消
    UPDATE oa_inventory_allocation_request 
    SET status = 4, update_time = UNIX_TIMESTAMP()
    WHERE status IN (1, 2) 
    AND request_time < (UNIX_TIMESTAMP() - max_wait_days * 86400);
    
    SELECT ROW_COUNT() INTO expired_count;
    
    -- 记录清理日志
    INSERT INTO oa_reverse_audit_log (
        ref_type, ref_id, ref_no, reason, operator_id, operator_name, create_time
    ) VALUES (
        'system_cleanup', 0, 'AUTO_CLEANUP', 
        CONCAT('自动清理过期分配需求，清理数量：', expired_count), 
        0, 'SYSTEM', UNIX_TIMESTAMP()
    );
    
END$$
DELIMITER ;

-- 9. 创建触发器：分配需求状态变更日志
DELIMITER $$
CREATE TRIGGER `tr_allocation_request_status_change` 
AFTER UPDATE ON `oa_inventory_allocation_request`
FOR EACH ROW
BEGIN
    IF OLD.status != NEW.status THEN
        INSERT INTO oa_inventory_allocation_history (
            request_id, product_id, warehouse_id, allocated_quantity,
            allocation_type, allocation_source, notes, created_by, create_time
        ) VALUES (
            NEW.id, NEW.product_id, NEW.warehouse_id, 
            NEW.allocated_quantity - OLD.allocated_quantity,
            'status_change', 
            CONCAT('状态变更: ', OLD.status, ' -> ', NEW.status),
            CONCAT('优先级: ', NEW.priority, ', 业务类型: ', NEW.ref_type),
            NEW.created_by, UNIX_TIMESTAMP()
        );
    END IF;
END$$
DELIMITER ;
