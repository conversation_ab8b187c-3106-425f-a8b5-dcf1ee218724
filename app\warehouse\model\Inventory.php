<?php
namespace app\warehouse\model;

use think\Model;

/**
 * 库存模型 - 兼容层
 * 这是一个兼容层，将旧的库存调用重定向到新的InventoryRealtime模型
 */
class Inventory extends Model
{
    protected $name = 'oa_inventory_realtime';
    protected $pk = 'id';
    
    // 字段映射，将旧字段名映射到新字段名
    protected $fieldMapping = [
        'quantity' => 'quantity',
        'available_quantity' => 'available_quantity',
        'allocated_quantity' => 'locked_quantity',
        'reserved_quantity' => 'locked_quantity'
    ];
    
    /**
     * 获取器 - 兼容旧字段名
     */
    public function getQuantityAttr($value, $data)
    {
        return $data['quantity'] ?? 0;
    }

    public function getAvailableQuantityAttr($value, $data)
    {
        return $data['available_quantity'] ?? 0;
    }

    public function getAllocatedQuantityAttr($value, $data)
    {
        return $data['locked_quantity'] ?? 0;
    }

    public function getReservedQuantityAttr($value, $data)
    {
        return $data['locked_quantity'] ?? 0;
    }
    
    /**
     * 修改器 - 兼容旧字段名
     */
    public function setQuantityAttr($value)
    {
        $this->data['quantity'] = $value;
        return $value;
    }

    public function setAvailableQuantityAttr($value)
    {
        $this->data['available_quantity'] = $value;
        return $value;
    }

    public function setAllocatedQuantityAttr($value)
    {
        $this->data['locked_quantity'] = $value;
        return $value;
    }

    public function setReservedQuantityAttr($value)
    {
        $this->data['locked_quantity'] = $value;
        return $value;
    }
    
    /**
     * 重写save方法以处理字段映射
     */
    public function save($data = [], $where = [], $sequence = null)
    {
        // 处理字段映射
        if (isset($this->data['allocated_quantity'])) {
            $this->data['locked_quantity'] = $this->data['allocated_quantity'];
            unset($this->data['allocated_quantity']);
        }
        if (isset($this->data['reserved_quantity'])) {
            $this->data['locked_quantity'] = $this->data['reserved_quantity'];
            unset($this->data['reserved_quantity']);
        }
        
        return parent::save($data, $where, $sequence);
    }
    
    /**
     * 静态方法兼容
     */
    public static function where($field, $op = null, $condition = null)
    {
        $instance = new static();
        
        // 处理字段映射
        if (isset($instance->fieldMapping[$field])) {
            $field = $instance->fieldMapping[$field];
        }
        
        return parent::where($field, $op, $condition);
    }
    
    /**
     * 查找方法兼容
     */
    public static function find($data = null)
    {
        $result = parent::find($data);
        if ($result) {
            // 确保返回的数据包含兼容字段
            $result->allocated_quantity = $result->locked_quantity;
            $result->reserved_quantity = $result->locked_quantity;
        }
        return $result;
    }
    
    /**
     * 选择方法兼容
     */
    public function select()
    {
        $results = parent::select();
        if ($results) {
            foreach ($results as $result) {
                $result->allocated_quantity = $result->locked_quantity;
                $result->reserved_quantity = $result->locked_quantity;
            }
        }
        return $results;
    }
}
