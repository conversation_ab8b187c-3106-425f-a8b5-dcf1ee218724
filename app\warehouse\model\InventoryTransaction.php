<?php
declare (strict_types = 1);

namespace app\warehouse\model;

use think\Model;

/**
 * 库存流水模型
 */
class InventoryTransaction extends Model
{
    // 设置表名
    protected $name = 'inventory_transaction';
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = false;
    
    // 交易类型常量
    const TYPE_IN = 'in';                    // 入库
    const TYPE_OUT = 'out';                  // 出库
    const TYPE_TRANSFER_OUT = 'transfer_out'; // 调拨出库
    const TYPE_TRANSFER_IN = 'transfer_in';   // 调拨入库
    const TYPE_ADJUST = 'adjust';            // 盘点调整
    const TYPE_LOCK = 'lock';                // 锁定
    const TYPE_UNLOCK = 'unlock';            // 解锁
    
    // 设置字段信息
    protected $schema = [
        'id'              => 'int',
        'transaction_no'  => 'string',
        'product_id'      => 'int',
        'warehouse_id'    => 'int',
        'transaction_type'=> 'string',
        'quantity'        => 'float',
        'before_quantity' => 'float',
        'after_quantity'  => 'float',
        'ref_type'        => 'string',
        'ref_id'          => 'int',
        'ref_no'          => 'string',
        'notes'           => 'string',
        'created_by'      => 'int',
        'create_time'     => 'int',
    ];
    
    /**
     * 关联产品
     */
    public function product()
    {
        return $this->belongsTo('app\product\model\Product', 'product_id', 'id');
    }
    
    /**
     * 关联仓库
     */
    public function warehouse()
    {
        return $this->belongsTo('app\warehouse\model\Warehouse', 'warehouse_id', 'id');
    }
    
    /**
     * 关联创建人
     */
    public function creator()
    {
        return $this->belongsTo('app\user\model\User', 'created_by', 'id');
    }
    
    /**
     * 获取交易类型文本
     */
    public function getTransactionTypeTextAttr($value, $data)
    {
        $type = isset($data['transaction_type']) ? $data['transaction_type'] : $this->transaction_type;
        $typeArray = [
            self::TYPE_IN => '入库',
            self::TYPE_OUT => '出库',
            self::TYPE_TRANSFER_OUT => '调拨出库',
            self::TYPE_TRANSFER_IN => '调拨入库',
            self::TYPE_ADJUST => '盘点调整',
            self::TYPE_LOCK => '锁定',
            self::TYPE_UNLOCK => '解锁'
        ];
        
        return isset($typeArray[$type]) ? $typeArray[$type] : '未知';
    }
    
    /**
     * 获取交易类型数组
     */
    public static function getTransactionTypeArr()
    {
        return [
            self::TYPE_IN => '入库',
            self::TYPE_OUT => '出库',
            self::TYPE_TRANSFER_OUT => '调拨出库',
            self::TYPE_TRANSFER_IN => '调拨入库',
            self::TYPE_ADJUST => '盘点调整',
            self::TYPE_LOCK => '锁定',
            self::TYPE_UNLOCK => '解锁'
        ];
    }

    /**
     * 获取关联类型数组
     */
    public static function getRefTypeArr()
    {
        return [
            'receipt' => '入库单',
            'outbound' => '出库单',
            'transfer' => '调拨单',
            'check' => '盘点单',
            'order' => '销售订单',
            'purchase' => '采购订单',
            'production' => '生产订单',
            'manual' => '手动操作',
            'system' => '系统操作',
            'lock' => '库存锁定',
            'other' => '其他'
        ];
    }

    /**
     * 生成流水号
     * 
     * @param string $type 交易类型
     * @return string
     */
    public static function generateTransactionNo($type = '')
    {
        $prefix = 'TXN';
        if ($type) {
            $typePrefix = [
                self::TYPE_IN => 'IN',
                self::TYPE_OUT => 'OUT',
                self::TYPE_TRANSFER_OUT => 'TO',
                self::TYPE_TRANSFER_IN => 'TI',
                self::TYPE_ADJUST => 'ADJ',
                self::TYPE_LOCK => 'LK',
                self::TYPE_UNLOCK => 'ULK'
            ];
            $prefix = $typePrefix[$type] ?? 'TXN';
        }
        
        return $prefix . date('Ymd') . str_pad((string)mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
    }
    
    /**
     * 创建库存流水记录
     * 
     * @param array $data 流水数据
     * @return InventoryTransaction
     */
    public static function createTransaction($data)
    {
        $transactionData = [
            'transaction_no' => $data['transaction_no'] ?? self::generateTransactionNo($data['transaction_type']),
            'product_id' => $data['product_id'],
            'warehouse_id' => $data['warehouse_id'],
            'transaction_type' => $data['transaction_type'],
            'quantity' => $data['quantity'],
            'before_quantity' => $data['before_quantity'],
            'after_quantity' => $data['after_quantity'],
            'ref_type' => $data['ref_type'] ?? '',
            'ref_id' => $data['ref_id'] ?? 0,
            'ref_no' => $data['ref_no'] ?? '',
            'notes' => $data['notes'] ?? '',
            'created_by' => $data['created_by'] ?? 0
        ];
        
        return self::create($transactionData);
    }
    
    /**
     * 记录入库流水
     * 
     * @param int $productId 产品ID
     * @param int $warehouseId 仓库ID
     * @param float $quantity 入库数量
     * @param float $beforeQuantity 入库前数量
     * @param float $afterQuantity 入库后数量
     * @param string $refType 关联类型
     * @param int $refId 关联ID
     * @param string $refNo 关联单号
     * @param string $notes 备注
     * @param int $createdBy 操作人
     * @return InventoryTransaction
     */
    public static function recordInbound($productId, $warehouseId, $quantity, $beforeQuantity, $afterQuantity, 
                                       $refType = '', $refId = 0, $refNo = '', $notes = '', $createdBy = 0)
    {
        return self::createTransaction([
            'product_id' => $productId,
            'warehouse_id' => $warehouseId,
            'transaction_type' => self::TYPE_IN,
            'quantity' => $quantity,
            'before_quantity' => $beforeQuantity,
            'after_quantity' => $afterQuantity,
            'ref_type' => $refType,
            'ref_id' => $refId,
            'ref_no' => $refNo,
            'notes' => $notes,
            'created_by' => $createdBy
        ]);
    }
    
    /**
     * 记录出库流水
     * 
     * @param int $productId 产品ID
     * @param int $warehouseId 仓库ID
     * @param float $quantity 出库数量（负数）
     * @param float $beforeQuantity 出库前数量
     * @param float $afterQuantity 出库后数量
     * @param string $refType 关联类型
     * @param int $refId 关联ID
     * @param string $refNo 关联单号
     * @param string $notes 备注
     * @param int $createdBy 操作人
     * @return InventoryTransaction
     */
    public static function recordOutbound($productId, $warehouseId, $quantity, $beforeQuantity, $afterQuantity, 
                                        $refType = '', $refId = 0, $refNo = '', $notes = '', $createdBy = 0)
    {
        return self::createTransaction([
            'product_id' => $productId,
            'warehouse_id' => $warehouseId,
            'transaction_type' => self::TYPE_OUT,
            'quantity' => -abs($quantity), // 确保是负数
            'before_quantity' => $beforeQuantity,
            'after_quantity' => $afterQuantity,
            'ref_type' => $refType,
            'ref_id' => $refId,
            'ref_no' => $refNo,
            'notes' => $notes,
            'created_by' => $createdBy
        ]);
    }
    
    /**
     * 记录调拨流水
     * 
     * @param int $productId 产品ID
     * @param int $fromWarehouseId 源仓库ID
     * @param int $toWarehouseId 目标仓库ID
     * @param float $quantity 调拨数量
     * @param float $fromBeforeQuantity 源仓库调拨前数量
     * @param float $fromAfterQuantity 源仓库调拨后数量
     * @param float $toBeforeQuantity 目标仓库调拨前数量
     * @param float $toAfterQuantity 目标仓库调拨后数量
     * @param string $refType 关联类型
     * @param int $refId 关联ID
     * @param string $refNo 关联单号
     * @param string $notes 备注
     * @param int $createdBy 操作人
     * @return array 返回两条流水记录
     */
    public static function recordTransfer($productId, $fromWarehouseId, $toWarehouseId, $quantity,
                                        $fromBeforeQuantity, $fromAfterQuantity, $toBeforeQuantity, $toAfterQuantity,
                                        $refType = '', $refId = 0, $refNo = '', $notes = '', $createdBy = 0)
    {
        // 调拨出库流水
        $outTransaction = self::createTransaction([
            'product_id' => $productId,
            'warehouse_id' => $fromWarehouseId,
            'transaction_type' => self::TYPE_TRANSFER_OUT,
            'quantity' => -abs($quantity), // 负数表示出库
            'before_quantity' => $fromBeforeQuantity,
            'after_quantity' => $fromAfterQuantity,
            'ref_type' => $refType,
            'ref_id' => $refId,
            'ref_no' => $refNo,
            'notes' => $notes,
            'created_by' => $createdBy
        ]);
        
        // 调拨入库流水
        $inTransaction = self::createTransaction([
            'product_id' => $productId,
            'warehouse_id' => $toWarehouseId,
            'transaction_type' => self::TYPE_TRANSFER_IN,
            'quantity' => $quantity, // 正数表示入库
            'before_quantity' => $toBeforeQuantity,
            'after_quantity' => $toAfterQuantity,
            'ref_type' => $refType,
            'ref_id' => $refId,
            'ref_no' => $refNo,
            'notes' => $notes,
            'created_by' => $createdBy
        ]);
        
        return [$outTransaction, $inTransaction];
    }
    
    /**
     * 搜索器：产品ID
     */
    public function searchProductIdAttr($query, $value, $data)
    {
        if (!empty($value)) {
            $query->where('product_id', $value);
        }
    }
    
    /**
     * 搜索器：仓库ID
     */
    public function searchWarehouseIdAttr($query, $value, $data)
    {
        if (!empty($value)) {
            $query->where('warehouse_id', $value);
        }
    }
    
    /**
     * 搜索器：交易类型
     */
    public function searchTransactionTypeAttr($query, $value, $data)
    {
        if (!empty($value)) {
            $query->where('transaction_type', $value);
        }
    }
    
    /**
     * 搜索器：关联类型
     */
    public function searchRefTypeAttr($query, $value, $data)
    {
        if (!empty($value)) {
            $query->where('ref_type', $value);
        }
    }
    
    /**
     * 搜索器：时间范围
     */
    public function searchTimeRangeAttr($query, $value, $data)
    {
        if (!empty($value)) {
            $timeRange = explode(',', $value);
            if (count($timeRange) == 2) {
                $query->whereBetween('create_time', [strtotime($timeRange[0]), strtotime($timeRange[1])]);
            }
        }
    }
    
    /**
     * 搜索器：关键字（流水号、关联单号）
     */
    public function searchKeywordsAttr($query, $value, $data)
    {
        if (!empty($value)) {
            $query->where(function($q) use ($value) {
                $q->whereOr('transaction_no', 'like', '%' . $value . '%')
                  ->whereOr('ref_no', 'like', '%' . $value . '%')
                  ->whereOr('notes', 'like', '%' . $value . '%');
            });
        }
    }
}
