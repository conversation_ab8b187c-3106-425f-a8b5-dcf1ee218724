<?php
declare (strict_types = 1);

namespace app\warehouse\model;

use think\Model;

/**
 * 库存分配历史记录模型
 */
class InventoryAllocationHistory extends Model
{
    // 设置表名
    protected $name = 'inventory_allocation_history';
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = false; // 历史记录不需要更新时间
    
    // 分配类型常量
    const TYPE_AUTO = 'auto';       // 自动分配
    const TYPE_MANUAL = 'manual';   // 手动分配
    
    // 分配来源常量
    const SOURCE_INBOUND = 'inbound';           // 入库分配
    const SOURCE_ADJUSTMENT = 'adjustment';     // 调整分配
    const SOURCE_RELEASE = 'release';           // 释放重分配
    const SOURCE_STATUS_CHANGE = 'status_change'; // 状态变更
    
    // 设置字段信息
    protected $schema = [
        'id'                 => 'int',
        'request_id'         => 'int',
        'product_id'         => 'int',
        'warehouse_id'       => 'int',
        'allocated_quantity' => 'float',
        'lock_id'            => 'int',
        'allocation_type'    => 'string',
        'allocation_source'  => 'string',
        'notes'              => 'string',
        'created_by'         => 'int',
        'create_time'        => 'int',
    ];
    
    /**
     * 关联分配需求
     */
    public function allocationRequest()
    {
        return $this->belongsTo('app\warehouse\model\InventoryAllocationRequest', 'request_id');
    }
    
    /**
     * 关联产品信息
     */
    public function product()
    {
        return $this->belongsTo('think\Model', 'product_id')->setTable('oa_product');
    }

    /**
     * 关联仓库信息
     */
    public function warehouse()
    {
        return $this->belongsTo('think\Model', 'warehouse_id')->setTable('oa_warehouse');
    }
    
    /**
     * 关联锁定记录
     */
    public function inventoryLock()
    {
        return $this->belongsTo('app\warehouse\model\InventoryLock', 'lock_id');
    }
    
    /**
     * 关联操作人信息
     */
    public function creator()
    {
        return $this->belongsTo('app\admin\model\Admin', 'created_by');
    }
    
    /**
     * 获取分配类型文本
     */
    public function getAllocationTypeTextAttr($value, $data)
    {
        $typeMap = [
            self::TYPE_AUTO => '自动分配',
            self::TYPE_MANUAL => '手动分配'
        ];
        
        return $typeMap[$data['allocation_type']] ?? '未知类型';
    }
    
    /**
     * 获取分配来源文本
     */
    public function getAllocationSourceTextAttr($value, $data)
    {
        $sourceMap = [
            self::SOURCE_INBOUND => '入库分配',
            self::SOURCE_ADJUSTMENT => '调整分配',
            self::SOURCE_RELEASE => '释放重分配',
            self::SOURCE_STATUS_CHANGE => '状态变更'
        ];
        
        return $sourceMap[$data['allocation_source']] ?? '未知来源';
    }
    
    /**
     * 创建分配历史记录
     */
    public static function createRecord($data)
    {
        $history = new self();
        $history->save([
            'request_id' => $data['request_id'],
            'product_id' => $data['product_id'],
            'warehouse_id' => $data['warehouse_id'],
            'allocated_quantity' => $data['allocated_quantity'],
            'lock_id' => $data['lock_id'] ?? 0,
            'allocation_type' => $data['allocation_type'] ?? self::TYPE_AUTO,
            'allocation_source' => $data['allocation_source'] ?? self::SOURCE_INBOUND,
            'notes' => $data['notes'] ?? '',
            'created_by' => $data['created_by'] ?? 0
        ]);
        
        return $history;
    }
    
    /**
     * 获取分配需求的历史记录
     */
    public static function getByRequest($requestId)
    {
        return self::where('request_id', $requestId)
            ->order('create_time DESC')
            ->select();
    }
    
    /**
     * 获取产品的分配历史
     */
    public static function getByProduct($productId, $warehouseId = null, $limit = 50)
    {
        $where = ['product_id' => $productId];
        
        if ($warehouseId) {
            $where['warehouse_id'] = $warehouseId;
        }
        
        return self::where($where)
            ->order('create_time DESC')
            ->limit($limit)
            ->select();
    }
    
    /**
     * 获取分配统计
     */
    public static function getAllocationStats($startTime = null, $endTime = null)
    {
        $where = [];
        
        if ($startTime) {
            $where[] = ['create_time', '>=', $startTime];
        }
        
        if ($endTime) {
            $where[] = ['create_time', '<=', $endTime];
        }
        
        return self::where($where)
            ->field([
                'allocation_type',
                'allocation_source',
                'COUNT(*) as count',
                'SUM(allocated_quantity) as total_quantity'
            ])
            ->group('allocation_type, allocation_source')
            ->select()
            ->toArray();
    }
    
    /**
     * 获取操作人的分配记录
     */
    public static function getByOperator($operatorId, $limit = 100)
    {
        return self::where('created_by', $operatorId)
            ->order('create_time DESC')
            ->limit($limit)
            ->select();
    }
    
    /**
     * 搜索器：按分配类型搜索
     */
    public function searchAllocationTypeAttr($query, $value)
    {
        if ($value !== '') {
            $query->where('allocation_type', $value);
        }
    }
    
    /**
     * 搜索器：按分配来源搜索
     */
    public function searchAllocationSourceAttr($query, $value)
    {
        if ($value !== '') {
            $query->where('allocation_source', $value);
        }
    }
    
    /**
     * 搜索器：按产品搜索
     */
    public function searchProductIdAttr($query, $value)
    {
        if ($value !== '') {
            $query->where('product_id', $value);
        }
    }
    
    /**
     * 搜索器：按仓库搜索
     */
    public function searchWarehouseIdAttr($query, $value)
    {
        if ($value !== '') {
            $query->where('warehouse_id', $value);
        }
    }
    
    /**
     * 搜索器：按时间范围搜索
     */
    public function searchCreateTimeAttr($query, $value)
    {
        if (is_array($value) && count($value) == 2) {
            $query->whereBetweenTime('create_time', $value[0], $value[1]);
        }
    }
}
