{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}

<div class="p-page">
    <div class="layui-card border-x border-t" style="margin-bottom:0; box-shadow:0 0 0 0 rgb(5 32 96 / 0%)">
        <div class="body-table layui-tab layui-tab-brief" lay-filter="tab">
            <ul class="layui-tab-title">
                <li class="layui-this">实时库存</li>
                <li>库存预警</li>
                <li>库存统计</li>
            </ul>
        </div> 
    </div>
    <form class="layui-form gg-form-bar border-x" id="barsearchform">
        <div class="layui-input-inline" style="width:150px;">
            <select name="warehouse_id">
                <option value="">选择仓库</option>
                <!-- 这里需要动态加载仓库列表 -->
            </select>
        </div>
        <div class="layui-input-inline" style="width:150px;">
            <input type="text" name="product_id" placeholder="产品ID" class="layui-input" autocomplete="off" />
        </div>
        <div class="layui-input-inline" style="width:220px;">
            <input type="text" name="keywords" placeholder="输入关键字，产品名称/编号" class="layui-input" autocomplete="off" />
        </div>
        <div class="layui-input-inline" style="width:150px">
            <input type="checkbox" name="show_all" value="1" title="显示所有产品" lay-skin="switch" lay-text="显示零库存|仅有库存" lay-filter="show_all">
        </div>
        <div class="layui-input-inline" style="width:150px">
            <input type="hidden" name="tab" value="0" />
            <button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="table-search"><i class="layui-icon layui-icon-search mr-1"></i>搜索</button>
            <button type="reset" class="layui-btn layui-btn-reset" lay-filter="table-reset">清空</button>
        </div>
    </form>
    <table class="layui-hide" id="table_inventory" lay-filter="table_inventory"></table>
</div>

<script type="text/html" id="toolbarDemo">
  <div class="layui-btn-container">
    <button class="layui-btn layui-btn-sm" lay-event="inbound">
        <span>+ 入库</span>
    </button>
    <button class="layui-btn layui-btn-sm layui-btn-normal" lay-event="outbound">
        <span>- 出库</span>
    </button>
    <button class="layui-btn layui-btn-sm layui-btn-warm" lay-event="transfer">
        <span>调拨</span>
    </button>
    <button class="layui-btn layui-btn-sm layui-btn-primary" lay-event="export">
        <span>导出</span>
    </button>
  </div>
</script>

{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
    const moduleInit = ['tool','tablePlus','element'];
    function gouguInit() {
        var table = layui.tablePlus, tool = layui.tool, element = layui.element;
        
        layui.pageTable = table.render({
            elem: "#table_inventory"
            ,title: "实时库存列表"
            ,toolbar: "#toolbarDemo"
            ,url: "index"
            ,page: true
            ,limit: 20
            ,cellMinWidth: 80
            ,height: 'full-152'
            ,cols: [[ //表头
                {
                    type: 'checkbox',
                    width: 50
                },{
                    field: 'id',
                    title: 'ID',
                    align: 'center',
                    width: 80,
                      templet: function(d) {
                         var id = d.product_id || (d.product ? d.product.id : '');
                         return id;
                      }
                },{
                    field: 'product_title',
                    title: '产品名称',
                    Width: 200,
                    templet: function(d) {
                        var title = d.product_title || (d.product ? d.product.title : '');
                        return '<div><a href="javascript:;" class="side-a" data-href="/warehouse/InventoryRealtimeController/detail/id/' + d.id + '.html">' + title + '</a></div>';
                    }
                },{
                    field: 'product_code',
                    title: '物品编码',
                    align: 'center',
                    width: 150,
                    templet: function(d) {
                        return d.product_code || (d.product ? d.product.material_code : '');
                    }
                },{
                    field: 'specs',
                    title: '规格',
                    align: 'center',
                    minWidth: 200,
                    templet: function(d) {
                        return d.specs || (d.product ? d.product.specs : '');
                    }
                },{
                    field: 'warehouse_name',
                    title: '仓库',
                    align: 'center',
                    width: 120,
                    templet: function(d) {
                        return d.warehouse_name || (d.warehouse ? d.warehouse.name : '');
                    }
                },{
                    field: 'quantity',
                    title: '总库存',
                    align: 'center',
                    width: 100,
                    templet: function(d) {
                        return '<span class="layui-badge layui-bg-blue">' + d.quantity + '</span>';
                    }
                },{
                    field: 'available_quantity',
                    title: '可用库存',
                    align: 'center',
                    width: 100,
                    templet: function(d) {
                        var color = d.available_quantity > 0 ? 'layui-bg-green' : 'layui-bg-gray';
                        return '<span class="layui-badge ' + color + '">' + d.available_quantity + '</span>';
                    }
                },{
                    field: 'locked_quantity',
                    title: '锁定库存',
                    align: 'center',
                    width: 100,
                    templet: function(d) {
                        var color = d.locked_quantity > 0 ? 'layui-bg-orange' : 'layui-bg-gray';
                        return '<span class="layui-badge ' + color + '">' + d.locked_quantity + '</span>';
                    }
                },{
                    field: 'unit',
                    title: '单位',
                    align: 'center',
                    width: 80
                },{
                    field: 'cost_price',
                    title: '成本价',
                    align: 'center',
                    width: 100,
                    templet: function(d) {
                        return '¥' + parseFloat(d.cost_price).toFixed(2);
                    }
                },{
                    field: 'update_time',
                    title: '更新时间',
                    align: 'center',
                    width: 160
                },{
                    field: 'right',
                    fixed:'right',
                    title: '操作',
                    width: 200,
                    align: 'center',
                    ignoreExport:true,
                    templet: function (d) {
                        var html = '<div class="layui-btn-group">';
                        var btn1='<span class="layui-btn layui-btn-xs" lay-event="inbound">入库</span>';
                        var btn2='<span class="layui-btn layui-btn-normal layui-btn-xs" lay-event="outbound">出库</span>';
                        var btn3='<span class="layui-btn layui-btn-warm layui-btn-xs" lay-event="lock">锁定</span>';
                        var btn4='<span class="layui-btn layui-btn-primary layui-btn-xs" lay-event="detail">详情</span>';
                        return html+btn1+btn2+btn3+btn4+'</div>';
                    }                        
                }
            ]]
        });
        
        //表头工具栏事件
        table.on('toolbar(table_inventory)', function(obj){
            if (obj.event === 'inbound'){
                tool.side("/warehouse/InventoryRealtimeController/inbound", "库存入库");
                return;
            }
            if (obj.event === 'outbound'){
                tool.side("/warehouse/InventoryRealtimeController/outbound", "库存出库");
                return;
            }
            if (obj.event === 'transfer'){
                tool.side("/warehouse/InventoryRealtimeController/transfer", "库存调拨");
                return;
            }
            if (obj.event === 'export'){
                window.open("/warehouse/InventoryRealtimeController/export?" + $("#barsearchform").serialize());
                return;
            }
        });    
            
        table.on('tool(table_inventory)',function (obj) {
            var data = obj.data;
            if (obj.event === 'detail') {
                tool.side("/warehouse/InventoryRealtimeController/detail?id="+data.product_id, "库存详情");
                return;
            }
            if (obj.event === 'inbound') {
                tool.side("/warehouse/InventoryRealtimeController/inbound?product_id="+data.product_id+"&warehouse_id="+data.warehouse_id, "库存入库");
                return;
            }
            if (obj.event === 'outbound') {
                tool.side("/warehouse/InventoryRealtimeController/outbound?product_id="+data.product_id+"&warehouse_id="+data.warehouse_id, "库存出库");
                return;
            }
            if (obj.event === 'lock') {
                tool.side("/warehouse/InventoryRealtimeController/lock?product_id="+data.product_id+"&warehouse_id="+data.warehouse_id, "库存锁定");
                return;
            }
        });
        
        // 表单搜索
        layui.form.on('submit(table-search)', function(data){
            var formData = data.field;
            layui.pageTable.reload({
                where: formData,
                page: {
                    curr: 1
                }
            });
            return false;
        });
        
        // 重置搜索
        layui.form.on('submit(table-reset)', function(data){
            $("#barsearchform")[0].reset();
            layui.pageTable.reload({
                where: {},
                page: {
                    curr: 1
                }
            });
            return false;
        });

        // 标签页切换事件
        element.on('tab(tab)', function(data){
            var tabIndex = data.index;
            $("input[name='tab']").val(tabIndex);

            // 根据标签页索引切换功能
            switch(tabIndex) {
                case 0: // 实时库存
                    layui.pageTable.reload({
                        url: 'index',
                        where: {tab: 0},
                        page: {curr: 1}
                    });
                    break;
                case 1: // 库存预警
                    layui.pageTable.reload({
                        url: 'alerts',
                        where: {tab: 1},
                        page: {curr: 1}
                    });
                    break;
                case 2: // 库存统计
                    layui.pageTable.reload({
                        url: 'statistics',
                        where: {tab: 2},
                        page: {curr: 1}
                    });
                    break;
            }
        });

        // 显示所有产品开关事件
        layui.form.on('switch(show_all)', function(data){
            var formData = $("#barsearchform").serialize();
            layui.pageTable.reload({
                where: layui.tool.parseParams(formData),
                page: {curr: 1}
            });
        });
    }
</script>
{/block}
